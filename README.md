# E-Learning Platform

## Table of Contents
- [Font Troubleshooting](#font-troubleshooting)
- [Payment Webhook Testing](#payment-webhook-testing)
- [Plugin Activation](#plugin-activation)
- [Git LFS Commands](#git-lfs-commands)
- [Translation Synchronization](#translation-synchronization)
- [Email Notifications](#email-notifications)

## Font Troubleshooting

To fix font-related issues, run the following commands:

```bash
git lfs prune --verify-remote
git lfs fetch --all
git lfs checkout
```

## Payment Webhook Testing

Test the payment webhook using the following curl command:

```bash
curl --location 'http://elearning.test/payment/sepay/hook' \
--header 'Content-Type: application/json' \
--header 'Authorization: Apikey e6AaQRl7A9fSeX4jJY9exfITAgk5KwGK' \
--data '{
    "gateway": "MBBank",
    "transactionDate": "2024-05-25 21:11:02",
    "accountNumber": "**********",
    "subAccount": null,
    "code": null,
    "content": "KLwAPrt5e7ve271rWoxK",
    "transferType": "out",
    "description": "Thanh toan QR SE123456",
    "transferAmount": 135000,
    "referenceCode": "FT123456789",
    "accumulated": 0,
    "id": 123456
}'
```

## Plugin Activation

To activate the PRO plugin, configure the following in your `.env` file:

```env
PACKAGE_NAME=PRO
```

## Git LFS Commands

When pulling content with filtering issues, use these commands:

```bash
git clean -fd
git config --local lfs.fetchinclude ""
```

## Translation Synchronization

To synchronize translations, run:

```bash
php artisan export:language-phrases 
php artisan db:seed --class=LanguagePhrasesSeeder

```
php artisan db:seed --class=AddonSeeder
## Email Notifications

The system sends emails in the following cases:

1. Account verification emails
2. Successful withdrawal confirmation
3. Commission received notifications


Thêm mới khai báo 1 page 
php artisan db:seed --class=BuilderPagesSeeder
demo
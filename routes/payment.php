<?php

use App\Http\Controllers\OfflinePaymentController;
use App\Http\Controllers\PaymentController;
use Illuminate\Support\Facades\Route;

Route::controller(PaymentController::class)->group(function () {
    Route::get('payment', 'index')->name('payment');
    Route::get('payment/show_payment_gateway_by_ajax/{identifier}', 'show_payment_gateway_by_ajax')->name('payment.show_payment_gateway_by_ajax');
    Route::get('payment/success/{identifier?}', 'payment_success')->name('payment.success');
    Route::get('payment/create/{identifier}', 'payment_create')->name('payment.create');

    // razor pay
    Route::post('payment/{identifier}/order', 'payment_razorpay')->name('razorpay.order');


    // sepay pay
    Route::post('payment/{identifier}/order', 'payment_sepay')->name('sepay.order');
    Route::post('payment/sepay/hook', 'sepay_paymentCallback')->name('sepay.hook');

    // paytm pay
    Route::get('payment/make/paytm/order', 'make_paytm_order')->name('make.paytm.order');
    Route::get('payment/make/{identifier}/status', 'paytm_paymentCallback')->name('payment.status');
    Route::get('payment/check/ajax', 'payment_check_ajax')->name('payment.check.ajax');
});

// purchase routes
Route::controller(\App\Http\Controllers\student\PurchaseController::class)->group(function () {
    Route::post('purchase/course/{course_id}', 'purchase_course')->name('purchase.course');
    Route::post('payout', 'payout')->name('payout');
    Route::get('purchase-history', 'purchase_history')->name('purchase.history');

});

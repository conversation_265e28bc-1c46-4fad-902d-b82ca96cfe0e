<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trang demo nhúng form đăng ký</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #1a365d;
            margin-bottom: 10px;
        }
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .code-sample {
            background: #282c34;
            color: #fff;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', Courier, monospace;
        }
        .embed-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 750px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Demo: Nhúng form đăng ký MSTs Academy</h1>
            <p>Đây là trang demo cách nhúng form đăng ký vào website bên ngoài</p>
        </div>

        <div class="main-content">
            <div class="section">
                <h2>Hướng dẫn nhúng form</h2>
                <p>Để nhúng form đăng ký MSTs Academy vào website của bạn, sử dụng mã HTML iframe sau:</p>
                <div class="code-sample">
                    &lt;iframe src="https://yourdomain.com/embed/register/your-course-slug?redirect_url=https://your-website.com/thank-you-page" width="100%" height="700" style="border:none;"&gt;&lt;/iframe&gt;
                </div>
                <p>Trong đó:</p>
                <ul>
                    <li><strong>your-course-slug</strong>: Slug của khóa học bạn muốn người dùng đăng ký</li>
                    <li><strong>redirect_url</strong>: URL của trang bạn muốn chuyển hướng sau khi đăng ký thành công</li>
                </ul>
            </div>

            <div class="section">
                <h2>Lắng nghe sự kiện đăng ký thành công</h2>
                <p>Bạn có thể lắng nghe sự kiện khi người dùng đăng ký thành công để thực hiện các hành động trên trang của bạn:</p>
                <div class="code-sample">
                    window.addEventListener('message', function(event) {
                        // Kiểm tra nguồn tin nhắn để đảm bảo an toàn
                        if (event.origin !== 'https://yourdomain.com') return;
                        
                        // Xử lý thông điệp
                        if (event.data && event.data.type === 'registration_success') {
                            // Người dùng đã đăng ký thành công
                            console.log('Đăng ký thành công! Chuyển hướng đến: ' + event.data.redirectUrl);
                            
                            // Có thể chuyển hướng người dùng
                            window.location.href = event.data.redirectUrl;
                            
                            // Hoặc thực hiện các hành động khác trên trang của bạn
                            // document.getElementById('successMessage').classList.remove('hidden');
                        }
                    });
                </div>
            </div>

            <div class="section">
                <h2>Demo nhúng form</h2>
                <p>Dưới đây là demo nhúng form đăng ký khóa học:</p>
                <div class="embed-container">
                    <!-- Thay đổi đường dẫn này thành đường dẫn thực tế của bạn -->
                    <iframe src="/embed/register/course-slug-demo?redirect_url=https://your-website.com/thank-you"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Lắng nghe sự kiện từ iframe
        window.addEventListener('message', function(event) {
            // Trong môi trường thực tế, hãy kiểm tra nguồn tin nhắn
            // if (event.origin !== 'https://yourdomain.com') return;
            
            if (event.data && event.data.type === 'registration_success') {
                console.log('Đăng ký thành công! Đang chuyển hướng đến: ' + event.data.redirectUrl);
                
                // Trong demo này, chỉ log thay vì thực sự chuyển hướng
                alert('Đăng ký thành công! Trong môi trường thực tế, người dùng sẽ được chuyển hướng đến: ' + event.data.redirectUrl);
            }
        });
    </script>
</body>
</html> 
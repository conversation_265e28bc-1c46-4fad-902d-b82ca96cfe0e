.main > .container {
  display: flex;
  gap: 20px;
  max-width: 1360px;
  padding: 24px 0;
}

.main .main-content {
  width: 66.66666666666667%;
}

.main .elearning-course-playlist {
  width: 33.33333333333333%;
}

.elearning-course .main-content .course-video-area {
  border: 0;
  padding: 0;
}

.main .elearning-course-playlist .course-list-container {
  max-height: calc(100vh - 50px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.elearning-course
  .elearning-course-playlist
  .course-list-container
  > .course-header {
  height: fit-content;
  flex-shrink: 0;
}

.elearning-course
  .elearning-course-playlist
  .course-list-container
  > .course-section {
  flex: 1 1 auto;
  height: auto;
  overflow-y: auto;
}

.elearning-course .course-video-info {
  margin-top: 16px;
}

.elearning-course .course-video-info-title {
  color: #111827;
  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px; /* 155.556% */
}

.elearning-course .course-video-info-meta {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 8px;
}

.elearning-course .course-video-info-meta .user-badge span {
  color: white;
}

.elearning-course .course-video-info-meta span {
  color: #4b5563;
  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; /* 150% */
}

.main-content .course-video-info-meta span > span {
  margin-left: 4px;
}

.main-content .course-video-info-meta .divider {
  width: 1px;
  height: 12px;
  border-radius: 9999px;
  display: inline-block;
  margin: 0 12px;
  background: #d1d5db;
}

.elearning-premium {
  width: 100%;
}

.elearning-premium .video-censorred.premium-video-container {
  background: rgba(0, 0, 0, 0.85);
  padding: 40px 20px;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  justify-content: center;
}

/* Premium video section specific styles */
.elearning-premium .premium-video-container {
  max-height: 567px;
  width: 100%;

  aspect-ratio: 16/9;
  border-radius: 8px;
}

.elearning-premium .premium-icon {
  margin-bottom: 12px;
}

.elearning-premium .premium-title {
  color: #ff8a00;
  font-weight: 500;
  margin-bottom: 16px;

  background: linear-gradient(92deg, #fe6d34 -0.1%, #facc15 100.1%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
}

.elearning-premium .premium-description {
  color: white;
  text-align: center;
  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  opacity: 0.8;
  margin-bottom: 24px;
}

.elearning-premium .premium-buttons {
  display: flex;
  gap: 12px;
}

.elearning-premium .btn-upgrade {
  border-radius: 8px;
  border: 1px solid #a5b4fc;
  background: linear-gradient(90deg, #818cf8 0%, #6366f1 100%);
  color: white;
  padding: 10px 20px;

  display: flex;
  align-items: center;
  gap: 12px;

  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.elearning-premium .btn-free {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 10px 16px 10px 20px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;

  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

/* Hover effects */
.elearning-premium .btn-upgrade:hover {
  background: #4f46e5;
  color: white;
}

.elearning-premium .btn-free:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.elearning-course .course-list-container > .course-header {
  gap: 0;
  margin-bottom: 0;
  border: 0;
}

.elearning-course .course-list-container {
  background: #fff;
  border-radius: 8px;
  font-family: "SVN-Mona Sans", sans-serif;
  border: 1px solid #6366f1;
  background: #6366f1;
}

.elearning-course .course-header > .nav-item {
  width: calc(50% - 12px);
}

.elearning-course .course-header > .nav-item.has-divider {
  width: calc(50% + 12px);
}

.elearning-course .course-header .course-divider {
  width: 24px;
  flex-shrink: 0;
  height: 100%;
  object-fit: cover;
}

.elearning-course .course-header .nav-link {
  height: 100%;
  width: 100%;
  padding: 0;
  border-radius: 0;
  display: flex;
  align-items: center;
  gap: 0;
  background-color: transparent;
}

.elearning-course .course-header .nav-link::after {
  display: none;
}

.elearning-course .course-header .nav-link:not(.active) > span {
  color: white;
}

.elearning-course .course-header .nav-link.active > span {
  position: relative;
  color: #6366f1;
}

.elearning-course .course-header .nav-link.active > span::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 1px;
  background-color: #6366f1;
}

.elearning-course .course-header > .course-menu span {
  border-top-left-radius: 7px;
}

.elearning-course .course-header > .course-update span {
  border-top-right-radius: 7px;
}

.elearning-course
  .course-header
  > .course-menu.nav-item
  > .nav-link:not(.active)
  > img {
  transform: scaleX(-1);
}

.elearning-course .course-header .nav-link > span {
  height: 100%;
  width: 100%;
  gap: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.elearning-course .course-header .nav-link.active > span {
  background-color: white;
}

.elearning-course .course-menu {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.elearning-course .course-update {
  color: #6366f1;
  font-size: 14px;
  font-weight: 500;
}

.elearning-course .course-section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #0f172a;
  margin: 0;
}

.elearning-course
  .course-list-container
  .accordion
  > .accordion-item:last-child {
  border-bottom: 0;
}

.elearning-course .course-list-container .accordion-item {
  border: 0;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0;

  background-color: transparent;
}

.elearning-course .course-section .accordion-body {
  border-top: 1px solid #e5e7eb;
}

.elearning-course .course-item-accordion-header.accordion-button {
  background-color: transparent;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  box-shadow: none;
}

.elearning-course
  .course-item-accordion-header.accordion-button
  > .course-item-accordion-header-icon {
  margin: 0;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);

  width: 24px;
  height: 24px;
}

.elearning-course
  .course-item-accordion-header.accordion-button[aria-expanded="true"]
  > .course-item-accordion-header-icon {
  transform: translateY(-50%) rotate(180deg);
}
.elearning-course .course-item-accordion-header.accordion-button::after {
  display: none;
}

.elearning-course .course-item-accordion-header:focus {
  box-shadow: none;
  border: none;
  outline: none;
  border-radius: 0;
}
.elearning-course .course-section {
  background: #fff;
  border-radius: 0 0 7px 7px;
  padding: 15px 0;
}

.elearning-course .course-section .course-item-accordion-header-title {
  color: #111827;
  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  letter-spacing: 0.5px;
}

.elearning-course .course-meta {
  margin-top: 4px;
  color: #6b7280;
  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 11px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 145.455% */

  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.elearning-course .course-meta .total-time-value,
.elearning-course .course-meta .lesson-count-value {
  font-weight: 500;
  margin-left: 4px;
}

.elearning-course .course-meta .divider {
  width: 1px;
  height: 12px;
  border-radius: 9999px;
  display: inline-block;
  margin: 0 12px;
  background: #d1d5db;
}

.elearning-course .course-section .accordion-body {
  padding: 16px;
}

.elearning-course .course-lessons > .lesson-item {
  margin-top: 16px;
}

.elearning-course .course-lessons > .lesson-item:first-child {
  margin-top: 0;
}

.elearning-course .course-lessons .lesson-content-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.elearning-course .lesson-thumbnail img {
  width: 24px;
  height: 24px;
  object-fit: cover;
}

.elearning-course .lesson-content {
  flex: 1;
}

.elearning-course .lesson-content a {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;

  color: #111827;
  text-overflow: ellipsis;
  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 13px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 153.846% */
  cursor: pointer;
}

.elearning-course .lesson-item a:hover {
  color: #6366f1;
}



.elearning-course .lesson-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-badge {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  gap: 6px;
  color: #fff;
  text-align: center;

  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 11px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
}

.user-badge.free-badge {
  border-radius: 5px;
  border: 1px solid #89b3ff;
  background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.user-badge.pro-badge {
  border-radius: 5px;
  border: 1px solid #ffa688;
  background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.elearning-course .duration {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;

  border-radius: 6px;
  border: 1px solid #f3f4f6;
  background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 100%);

  color: #4b5563;
  text-overflow: ellipsis;
  font-family: "SVN-Mona Sans", Inter, sans-serif;
  font-size: 11px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
}

.main-content .course-video-navtab {
  box-shadow: none;
  margin-top: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 0;
  background: #fff;
}

.main-content .course-video-navtab > .nav-pills {
  padding: 0 20px;
  margin-bottom: 0;
}

.main-content .course-video-navtab > .tab-content > .tab-pane {
  padding: 20px;
}

.main-content
  .course-video-navtab
  > .tab-content
  .tab-pane.course-list-container {
  padding: 0;
}

.main-content .course-video-navtab > .nav-pills .nav-link {
  padding: 14px 0;
}

.course-video-navtab .course-menu-tab {
  display: none;
}

.course-video-navtab .course-list-container {
  display: none;
}

.course-video-navtab .nav-pills .nav-link:focus,
.course-video-navtab .nav-pills .nav-link:focus-visible {
  box-shadow: none;
}

@media (max-width: 1360px) {
  .main > .container {
    justify-content: center;
    padding: 24px;
  }

  .main .main-content {
    width: 66.66666666666667%;
    min-width: 900px;
  }

  .elearning-course .elearning-course-playlist {
    display: none;
  }

  .course-video-navtab .course-menu-tab {
    display: block;
  }

  .course-video-navtab .course-list-container.show {
    display: block;
  }
}

@media (max-width: 1024px) {
  .main .main-content {
    width: 100%;
    min-width: unset;
  }
}

@media (max-width: 768px) {
  .main > .container {
    padding: 24px 16px;
  }

  .elearning-premium .premium-title {
    font-size: 20px;
    line-height: 1.15;
  }

  .main .elearning-course .table-responsive > .table {
    min-width: 700px;
  }

  .elearning-course .forum-tab-content .add-question {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .elearning-course .forum-tab-content .add-question #create-question {
    margin-left: auto;
    padding: 8px 16px;
    font-size: 14px;
  }

  .elearning-course .questions .parent-review .review-title {
    white-space: normal;
  }
  .elearning-course .questions .parent-review .review-content {
    white-space: normal;
  }

  .elearning-course .questions .parent-review .review-actions {
    flex-wrap: wrap;
  }

  .elearning-course .questions .parent-review + .child-review {
    padding-left: 1rem !important;
  }
}

@media (max-width: 640px) {
  .main > .container {
    padding: 0;
    padding-bottom: 24px;
  }

  .elearning-course .main-content .course-video-area {
    border-radius: 0;
  }

  .main-content .course-video-navtab {
    margin-top: 16px;
    border-radius: 0;
    border-left: 0;
    border-right: 0;
    border-bottom: 0;
  }

  .main-content .course-video-navtab > .nav-pills {
    padding: 0 16px;
    gap: 20px;
    overflow: auto hidden;
    max-width: 100%;
    flex-wrap: nowrap;
  }

  .course-video-navtab .nav-pills .nav-link > span {
    white-space: nowrap;
  }

  .elearning-course .course-header > .course-menu span {
    border-top-left-radius: 0;
  }
  .elearning-course .course-header > .course-update span {
    border-top-right-radius: 0;
  }

  .elearning-course .course-list-container {
    border: 0;
    border-radius: 0;
  }

  .elearning-course .course-video-info {
    margin-top: 12px;
    padding: 0 16px;
  }

  .elearning-course .course-video-wrap,
  .elearning-premium .premium-video-container {
    border-radius: 0;
  }

  .elearning-premium .video-censorred.premium-video-container {
    padding: 20px 24px;
  }

  .elearning-course .course-section {
    border-radius: 0;
  }

  .elearning-premium .premium-title {
    font-size: 16px;
  }

  .elearning-premium .premium-description {
    margin-bottom: 12px;
  }

  .elearning-premium .premium-buttons {
    flex-direction: column;
  }
  .elearning-premium .btn-upgrade,
  .elearning-premium .btn-free {
    padding: 8px 12px;
    justify-content: center;
  }

  .elearning-premium .btn-upgrade img,
  .elearning-premium .btn-free img {
    width: 16px;
  }
  .elearning-course .course-header .nav-link > span {
    font-size: 12px;
    line-height: 1.25;
  }
}

<svg width="497" height="130" viewBox="0 0 497 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M72.9722 129.978L21.3877 111.728H474L427.408 129.978H72.9722Z" fill="url(#paint0_linear_7428_122)"/>
<g filter="url(#filter0_d_7428_122)">
<rect x="15" y="15" width="466.301" height="96.7277" rx="5" fill="url(#paint1_linear_7428_122)"/>
<rect x="16" y="16" width="464.301" height="94.7277" rx="4" stroke="url(#paint2_linear_7428_122)" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_d_7428_122" x="0" y="0" width="496.301" height="126.728" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7428_122"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7428_122" result="shape"/>
</filter>
<linearGradient id="paint0_linear_7428_122" x1="247.694" y1="111.728" x2="247.694" y2="129.978" gradientUnits="userSpaceOnUse">
<stop stop-color="#0153DD"/>
<stop offset="1" stop-color="#003A84"/>
</linearGradient>
<linearGradient id="paint1_linear_7428_122" x1="15" y1="63.3639" x2="481.301" y2="63.3639" gradientUnits="userSpaceOnUse">
<stop stop-color="#064FD0"/>
<stop offset="1" stop-color="#0043C0"/>
</linearGradient>
<linearGradient id="paint2_linear_7428_122" x1="248.15" y1="15" x2="248.15" y2="111.728" gradientUnits="userSpaceOnUse">
<stop stop-color="#162E74"/>
<stop offset="1" stop-color="#0046AE"/>
</linearGradient>
</defs>
</svg>

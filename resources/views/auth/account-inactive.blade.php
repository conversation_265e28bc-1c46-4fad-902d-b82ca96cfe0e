@extends('layouts' . '.' . get_frontend_settings('theme'))
@push('title', get_phrase('Account Inactive'))
@push('meta')@endpush
@push('css')
@endpush
@section('content')
    <!------------------- Login Area Start  ------>
    <section class="login-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-7 col-md-6">
                    <div class="login-img text-center">
                        <img class="w-75 h-auto ms-auto me-auto" src="{{ asset('assets/frontend/default/image/login.gif') }}" alt="...">
                    </div>
                </div>
                <div class="col-lg-5 col-md-6">
                    <div class="global-form login-form mt-25">
                        <h4 class="g-title">{{ get_phrase('Account Inactive') }}</h4>
                        <p class="description">{{ get_phrase('Your account is currently inactive. Please contact the administrator for assistance.') }}</p>

                        @if (session('error'))
                            <p class="description mt-4 text-danger">{{ session('error') }}</p>
                        @endif
                        
                        <a href="{{ route('login') }}" class="eBtn gradient w-100 mt-5">{{ get_phrase('Back to Login') }}</a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!------------------- Login Area End  ------>
@endsection
@push('js')
@endpush 
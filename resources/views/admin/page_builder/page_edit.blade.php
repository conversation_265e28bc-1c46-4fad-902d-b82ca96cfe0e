<form action="{{ route('admin.page.update', ['id' => $id]) }}" method="post" enctype="multipart/form-data">
    @CSRF

    <div class="row">
        <div class="col-12">
            <div class="mb-3">
                <label for="name" class="form-label ol-form-label">{{ get_phrase('Name') }}</label>
                <input type="text" name="name" class="form-control ol-form-control" id="name" placeholder="{{ get_phrase('Enter your page name') }}" aria-label="{{ get_phrase('Enter your page name') }}" value="{{ App\Models\Builder_page::where('id', $id)->first()->name }}" required />
            </div>
            <div class="mb-3">
                <div class="form-check">
                    <input type="checkbox" name="is_page_courses" value="1" class="form-check-input eRadioSuccess" id="is_page_courses" {{ App\Models\Builder_page::where('id', $id)->first()->is_page_courses == 1 ? 'checked' : '' }}>
                    <label for="detail_page_flag" class="form-check-label">{{get_phrase('Course Details Page')}}</label>
                </div>
            </div>
            <div class="mb-3">
                <div class="form-check">
                    <input type="checkbox" name="disable_bootstrap" value="1" class="form-check-input eRadioSuccess" id="disable_bootstrap" {{ App\Models\Builder_page::where('id', $id)->first()->disable_bootstrap == 1 ? 'checked' : '' }}>
                    <label for="disable_bootstrap" class="form-check-label">{{get_phrase('Disable Bootstrap')}}</label>
                </div>
            </div>
            <div class="mb-2">
                <button type="submit" class="btn ol-btn-primary">{{ get_phrase('Submit') }}</button>
            </div>
        </div>
    </div>
</form>

@extends('layouts.admin')

@push('title', get_phrase('Access Denied'))

@push('meta')
    <meta name="csrf-token" content="{{ csrf_token() }}" />
@endpush

@push('css')
    <style>
        .access-denied-container {
            padding: 60px 0;
            max-width: 900px;
            margin: 0 auto;
        }
        
        .access-denied-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            background: linear-gradient(to right, #ffffff, #f8f9fa);
        }
        
        .access-denied-icon {
            font-size: 8rem;
            color: #dc3545;
            margin-bottom: 1.5rem;
            filter: drop-shadow(0 5px 10px rgba(220, 53, 69, 0.2));
            transition: all 0.3s ease;
        }
        
        .access-denied-card:hover .access-denied-icon {
            transform: scale(1.05);
        }
        
        .access-denied-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #212529;
            margin-bottom: 1rem;
        }
        
        .access-denied-message {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #6c757d;
            max-width: 80%;
            margin: 0 auto 2rem;
        }
        
        .access-denied-info {
            background-color: rgba(220, 53, 69, 0.1);
            border-left: 4px solid #dc3545;
            padding: 1rem 1.5rem;
            border-radius: 0 8px 8px 0;
            margin-bottom: 2rem;
            margin-top: 1rem;
        }
        
        .info-heading {
            font-weight: 600;
            color: #dc3545;
            margin-bottom: 0.5rem;
        }
        
        .action-btn {
            font-weight: 500;
            padding: 10px 24px;
            border-radius: 50px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            margin: 0 10px;
        }
        
        .btn-go-back {
            background-color: #dc3545;
            border-color: #dc3545;
            box-shadow: 0 4px 10px rgba(220, 53, 69, 0.2);
        }
        
        .btn-go-back:hover, .btn-go-back:focus {
            background-color: #c82333;
            border-color: #bd2130;
            box-shadow: 0 6px 15px rgba(220, 53, 69, 0.3);
            transform: translateY(-2px);
        }
        
        .btn-dashboard {
            background-color: #6c757d;
            border-color: #6c757d;
            box-shadow: 0 4px 10px rgba(108, 117, 125, 0.2);
        }
        
        .btn-dashboard:hover, .btn-dashboard:focus {
            background-color: #5a6268;
            border-color: #545b62;
            box-shadow: 0 6px 15px rgba(108, 117, 125, 0.3);
            transform: translateY(-2px);
        }
        
        .features-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            margin-bottom: 30px;
        }
        
        .feature-item {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            width: 150px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-top: 3px solid #dc3545;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }
        
        .feature-icon {
            font-size: 1.8rem;
            color: #dc3545;
            margin-bottom: 12px;
        }
        
        .feature-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0;
        }
    </style>
@endpush

@section('content')
    <div class="access-denied-container">
        <div class="card access-denied-card">
            <div class="card-body text-center p-5">
                <i class="fas fa-exclamation-circle access-denied-icon"></i>
                
                <h1 class="access-denied-title">{{ get_phrase('Access Denied') }}</h1>
                
                <p class="access-denied-message">
                    {{ get_phrase('You do not have permission to access this feature. Please contact your administrator for assistance or upgrade to a plan that includes this feature.') }}
                </p>
                
                <div class="access-denied-info mx-auto" style="max-width: 600px;">
                    <p class="info-heading">{{ get_phrase('Why am I seeing this?') }}</p>
                    <p class="mb-0">{{ get_phrase('This feature requires additional permissions or a higher subscription plan. Your current account level does not include access to this functionality.') }}</p>
                </div>
                
                <div class="features-list">
                    <div class="feature-item">
                        <i class="fas fa-crown feature-icon"></i>
                        <p class="feature-title">{{ get_phrase('Premium Feature') }}</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-lock feature-icon"></i>
                        <p class="feature-title">{{ get_phrase('Restricted Access') }}</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-arrow-up feature-icon"></i>
                        <p class="feature-title">{{ get_phrase('Upgrade Required') }}</p>
                    </div>
                </div>
                
                <div class="d-flex justify-content-center mt-4">
                    <a href="javascript:history.back()" class="btn btn-go-back action-btn btn-danger">
                        <i class="fas fa-arrow-left me-2"></i>{{ get_phrase('Go Back') }}
                    </a>
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-dashboard action-btn btn-secondary">
                        <i class="fas fa-home me-2"></i>{{ get_phrase('Dashboard') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection
@extends('layouts.admin')
@push('title', 'Test TinyMCE Editor')
@push('meta')@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="ol-card">
                <div class="ol-card-body p-4">
                    <h4 class="mb-4">📝 Test TinyMCE Editor - Hỗ trợ Copy từ Word</h4>
                    
                    <div class="alert alert-info">
                        <h5>🔧 Hướng dẫn sử dụng:</h5>
                        <ul class="mb-0">
                            <li><strong>Copy từ Word:</strong> Chọn nội dung trong Word → Ctrl+C → Paste vào editor</li>
                            <li><strong>Giữ định dạng:</strong> Editor sẽ tự động giữ nguyên font, màu sắc, table, list...</li>
                            <li><strong>Xem kết quả:</strong> Nhấn "Xem HTML" để kiểm tra code được tạo</li>
                        </ul>
                    </div>

                    <form>
                        <div class="mb-3">
                            <label class="form-label" for="tinymce-test">Nội dung Blog:</label>
                            <textarea name="description" class="form-control" id="tinymce-test">
                                <h2>Chào mừng đến với TinyMCE Editor!</h2>
                                <p>Bạn có thể copy nội dung từ <strong>Microsoft Word</strong> và paste vào đây để giữ nguyên định dạng.</p>
                                <ul>
                                    <li>Định dạng <em>italic</em> và <strong>bold</strong></li>
                                    <li>Màu sắc và font chữ</li>
                                    <li>Bảng biểu và danh sách</li>
                                    <li>Hình ảnh (nếu có)</li>
                                </ul>
                                <table border="1" style="border-collapse: collapse; width: 100%;">
                                    <thead>
                                        <tr style="background-color: #f0f0f0;">
                                            <th>Cột 1</th>
                                            <th>Cột 2</th>
                                            <th>Cột 3</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Dữ liệu 1</td>
                                            <td>Dữ liệu 2</td>
                                            <td>Dữ liệu 3</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </textarea>
                        </div>

                        <div class="text-center mb-3">
                            <button type="button" class="btn btn-primary" onclick="getContent()">📄 Xem HTML</button>
                            <button type="button" class="btn btn-success" onclick="setContent()">📝 Thêm nội dung mẫu</button>
                            <button type="button" class="btn btn-warning" onclick="clearContent()">🗑️ Xóa nội dung</button>
                        </div>
                    </form>

                    <div class="mt-4">
                        <h5>🔍 HTML Output:</h5>
                        <div id="content-preview" style="min-height: 100px; background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;">
                            <em>HTML sẽ hiển thị ở đây khi bạn nhấn "Xem HTML"</em>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.tox-tinymce {
    border-radius: 8px !important;
}
</style>
@endpush

@push('js')
<!-- TinyMCE CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize TinyMCE with enhanced Word support
    tinymce.init({
        selector: '#tinymce-test',
        height: 500,
        
        // Plugins hỗ trợ copy từ Word
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste',
            'importcss', 'autosave', 'save', 'textcolor', 'colorpicker'
        ],
        
        // Toolbar với đầy đủ chức năng
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | ' +
                'forecolor backcolor | align lineheight | ' +
                'bullist numlist outdent indent | removeformat | ' +
                'table tabledelete | tableprops tablerowprops tablecellprops | ' +
                'tableinsertrowbefore tableinsertrowafter tabledeleterow | ' +
                'tableinsertcolbefore tableinsertcolafter tabledeletecol | ' +
                'link image media | code preview fullscreen',
        
        // Cấu hình Paste để chấp nhận mọi nội dung từ Word
        paste_as_text: false,
        paste_auto_cleanup_on_paste: false,
        paste_data_images: true,
        paste_enable_default_filters: false,
        paste_remove_spans: false,
        paste_remove_styles: false,
        paste_remove_styles_if_webkit: false,
        paste_strip_class_attributes: 'none',
        paste_merge_formats: true,
        paste_convert_word_fake_lists: true,
        paste_retain_style_properties: 'all',
        
        // Cho phép tất cả các element và attribute
        valid_elements: '*[*]',
        valid_children: '+body[style],+div[p|br|span]',
        extended_valid_elements: '*[*]',
        
        // Xử lý paste content từ Word
        paste_preprocess: function(plugin, args) {
            console.log('Đang paste nội dung từ Word...');
            console.log('Nội dung gốc:', args.content);
            
            // Chỉ xóa một số thuộc tính không cần thiết của Office
            args.content = args.content.replace(/<!--[\s\S]*?-->/g, ''); // Xóa comment
        },
        
        paste_postprocess: function(plugin, args) {
            console.log('Hoàn thành paste từ Word');
            // Tự động hiển thị HTML sau khi paste
            setTimeout(getContent, 500);
        },
        
        // Font và style
        font_family_formats: 'Arial=arial,helvetica,sans-serif; Times New Roman=times new roman,times,serif; Courier New=courier new,courier,monospace; Verdana=verdana,geneva,sans-serif; Georgia=georgia,serif; Palatino=palatino linotype,book antiqua,palatino,serif; Tahoma=tahoma,arial,helvetica,sans-serif; Trebuchet MS=trebuchet ms,geneva,sans-serif; Lucida Sans=lucida sans unicode,lucida grande,sans-serif; Impact=impact,chicago,sans-serif; Comic Sans MS=comic sans ms,cursive',
        
        font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 26pt 28pt 30pt 32pt 34pt 36pt 38pt 40pt 42pt 44pt 46pt 48pt 50pt 52pt 54pt 56pt 58pt 60pt 62pt 64pt 66pt 68pt 70pt 72pt 74pt 76pt 78pt 80pt',
        
        // Cấu hình bảng
        table_default_attributes: {
            border: '1'
        },
        table_default_styles: {
            'border-collapse': 'collapse',
            'width': '100%'
        },
        
        // Cấu hình khác
        menubar: 'file edit view insert format tools table help',
        contextmenu: 'link image table',
        branding: false,
        promotion: false,
        
        // Xử lý khi editor sẵn sàng
        setup: function(editor) {
            editor.on('init', function() {
                console.log('TinyMCE đã sẵn sàng cho test!');
            });
            
            editor.on('paste', function(e) {
                console.log('Đã paste nội dung vào test editor');
            });
        }
    });
});

// Hàm lấy nội dung HTML
function getContent() {
    const content = tinymce.get('tinymce-test').getContent();
    document.getElementById('content-preview').innerHTML = '<pre><code>' + 
        content.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</code></pre>';
}

// Hàm thêm nội dung mẫu
function setContent() {
    const sampleContent = `
        <h2 style="color: #2196F3;">Tiêu đề mẫu từ Word</h2>
        <p>Đây là một đoạn văn bản mẫu với <strong>định dạng đậm</strong> và <em>định dạng nghiêng</em>.</p>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <thead>
                <tr style="background-color: #f0f0f0;">
                    <th>Cột 1</th>
                    <th>Cột 2</th>
                    <th>Cột 3</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Dữ liệu 1</td>
                    <td>Dữ liệu 2</td>
                    <td>Dữ liệu 3</td>
                </tr>
            </tbody>
        </table>
        <ul>
            <li>Mục danh sách 1</li>
            <li>Mục danh sách 2</li>
            <li>Mục danh sách 3</li>
        </ul>
    `;
    tinymce.get('tinymce-test').setContent(sampleContent);
}

// Hàm xóa nội dung
function clearContent() {
    tinymce.get('tinymce-test').setContent('');
    document.getElementById('content-preview').innerHTML = '<em>HTML sẽ hiển thị ở đây khi bạn nhấn "Xem HTML"</em>';
}
</script>
@endpush

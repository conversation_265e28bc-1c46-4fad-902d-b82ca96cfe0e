@extends('layouts.admin')

@push('title', get_phrase('Popup Management'))

@push('meta')
@endpush

@push('css')
@endpush

@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    {{ get_phrase('Popup Management') }}
                </h4>

                <a href="#" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px" data-bs-toggle="modal" data-bs-target="#add_popup">
                    <span class="fi-rr-plus"></span>
                    <span>{{ get_phrase('Add New Popup') }}</span>
                </a>
            </div>
        </div>
    </div>

    <div class="ol-card p-4">
        <div class="ol-card-body">
            <div class="row mt-4">
                <div class="col-md-12">
                    @if (count($popups) > 0)
                        <div class="table-responsive" id="popup_list">
                            <table class="table eTable eTable-2 print-table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">{{ get_phrase('Image') }}</th>
                                        <th scope="col">{{ get_phrase('Name') }}</th>
                                        <th scope="col">{{ get_phrase('URL') }}</th>
                                        <th scope="col">{{ get_phrase('Status') }}</th>
                                        <th class="print-d-none" scope="col">{{ get_phrase('Options') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($popups as $key => $popup)
                                    <tr>
                                        <th scope="row">
                                            <p class="row-number">{{ $key+1 }}</p>
                                        </th>
                                        <td>
                                            <img src="{{ get_image($popup->image_link) }}" alt="Popup" class="img-fluid" style="max-height: 100px; max-width: 200px;">
                                        </td>
                                        <td>
                                            <div class="dAdmin_info_name min-w-150px">
                                                <p class="fw-bold">{{ $popup->name ?? '-' }}</p>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="dAdmin_info_name min-w-150px">
                                                <p>{{ $popup->url ?? '-' }}</p>
                                            </div>
                                        </td>
                                        <td>
                                            @if($popup->status == 'active')
                                                <span class="badge bg-success">{{ get_phrase('Active') }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ get_phrase('Inactive') }}</span>
                                            @endif
                                        </td>
                                        <td class="print-d-none">
                                            <div class="dropdown ol-icon-dropdown ol-icon-dropdown-transparent">
                                                <button class="btn ol-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span class="fi-rr-menu-dots-vertical"></span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#edit_popup_{{$popup->id}}">
                                                            {{ get_phrase('Edit') }}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.marketing.popup.delete', ['id' => $popup->id]) }}" onclick="return confirm('{{ get_phrase('Are you sure you want to delete this popup?') }}')">
                                                            {{ get_phrase('Delete') }}
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <!-- Edit Popup Modal -->
                                    <div class="modal fade" id="edit_popup_{{$popup->id}}" tabindex="-1" aria-labelledby="edit_popupLabel" aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="edit_popupLabel">{{ get_phrase('Edit Popup') }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <form method="POST" action="{{ route('admin.marketing.popup.update', ['id' => $popup->id]) }}" enctype="multipart/form-data">
                                                    @csrf
                                                    <div class="modal-body">
                                                        <div class="mb-3">
                                                            <label for="name" class="form-label">{{ get_phrase('Name') }}</label>
                                                            <input type="text" class="form-control ol-form-control" name="name" value="{{ $popup->name }}">
                                                            <small class="text-muted">{{ get_phrase('A descriptive name to identify this popup') }}</small>
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="current_image" class="form-label">{{ get_phrase('Current Image') }}</label>
                                                            <div>
                                                                <img src="{{ get_image($popup->image_link) }}" alt="Current Image" class="img-fluid" style="max-height: 200px">
                                                            </div>
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="image" class="form-label">{{ get_phrase('New Image') }}</label>
                                                            <input type="file" class="form-control ol-form-control" name="image" accept="image/*">
                                                            <small class="text-muted">{{ get_phrase('Leave empty to keep the current image') }}</small>
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="url" class="form-label">{{ get_phrase('URL') }}</label>
                                                            <input type="url" class="form-control ol-form-control" name="url" value="{{ $popup->url }}">
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="status" class="form-label">{{ get_phrase('Status') }}</label>
                                                            <select class="form-select ol-form-control" name="status">
                                                                <option value="active" {{ $popup->status == 'active' ? 'selected' : '' }}>{{ get_phrase('Active') }}</option>
                                                                <option value="inactive" {{ $popup->status == 'inactive' ? 'selected' : '' }}>{{ get_phrase('Inactive') }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn ol-btn-secondary" data-bs-dismiss="modal">{{ get_phrase('Close') }}</button>
                                                        <button type="submit" class="btn ol-btn-primary">{{ get_phrase('Update') }}</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        @include('admin.no_data')
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Add Popup Modal -->
    <div class="modal fade" id="add_popup" tabindex="-1" aria-labelledby="add_popupLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="add_popupLabel">{{ get_phrase('Add New Popup') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="{{ route('admin.marketing.popup.store') }}" enctype="multipart/form-data">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ get_phrase('Name') }} <span class="required">*</span></label>
                            <input type="text" class="form-control ol-form-control" name="name" required>
                            <small class="text-muted">{{ get_phrase('A descriptive name to identify this popup') }}</small>
                        </div>
                        <div class="mb-3">
                            <label for="image" class="form-label">{{ get_phrase('Image') }} <span class="required">*</span></label>
                            <input type="file" class="form-control ol-form-control" name="image" accept="image/*" required>
                        </div>
                        <div class="mb-3">
                            <label for="url" class="form-label">{{ get_phrase('URL') }}</label>
                            <input type="url" class="form-control ol-form-control" name="url" placeholder="https://example.com">
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">{{ get_phrase('Status') }}</label>
                            <select class="form-select ol-form-control" name="status">
                                <option value="active">{{ get_phrase('Active') }}</option>
                                <option value="inactive" selected>{{ get_phrase('Inactive') }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn ol-btn-secondary" data-bs-dismiss="modal">{{ get_phrase('Close') }}</button>
                        <button type="submit" class="btn ol-btn-primary">{{ get_phrase('Save') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('js')
@endpush 
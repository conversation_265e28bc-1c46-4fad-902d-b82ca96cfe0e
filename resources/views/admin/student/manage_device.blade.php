@extends('layouts.admin')
@push('title', get_phrase('<PERSON><PERSON>ản lý thiết bị người dùng'))
@push('meta')@endpush
@push('css')@endpush

@section('content')
<div class="row">
    <div class="col-12">
        <div class="ol-card p-4">
            <div class="ol-card-body">
                <div class="container p-5">

                    <div class="row">
                        <div class="col-12">
                            <h4 class="title fs-16px mb-3">
                                <i class="fi-rr-settings-sliders me-2"></i>
                                {{ get_phrase('Thiết bị của') }} {{ $user->name ?? 'N/A' }}
                            </h4>

                            <div class="table-responsive">
                                <table class="table mt-4">
                                    <thead>
                                    <tr>
                                        <th>Tên thiết bị</th>
                                        <th>IP</th>
                                        <th class="text-right">Thời gian đăng nhập</th>
                                        <th class="text-right">Hành động</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @forelse($devices as $device)
                                    <tr>
                                        <td>{{ $device->device_name }}</td>
                                        <td>{{ $device->ip_address }}</td>
                                        <td class="text-right">
                                            {{ $device->updated_at->format('d/m/Y H:i') }}
                                            @if($device->session_id == session()->getId())
                                                <span class="badge badge-success">Hiện tại</span>
                                            @endif
                                        </td>
                                        <td class="text-right">
                                            @if($device->session_id != session()->getId())
                                            <a href="{{ route('admin.student.device.delete', ['id' => $device->id, 'user_id' => $user->id]) }}"
                                               class="btn btn-sm btn-danger"
                                               onclick="return confirm('Bạn có chắc chắn muốn xóa thiết bị này?')">
                                                <i class="fas fa-trash"></i> Xóa
                                            </a>
                                            @endif
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="4" class="text-center">Không có thiết bị nào được ghi nhận</td>
                                    </tr>
                                    @endforelse
                                    </tbody>
                                </table>
                            </div> <!-- end table-responsive-->
                        </div> <!-- end col -->
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <strong>Lưu ý:</strong> Người dùng này có {{ $devices->count() }} thiết bị đã đăng nhập và {{ $user->account_violations ?? 0 }} lần vi phạm sử dụng tài khoản đồng thời.
                            </div>

                            @if(($user->account_violations ?? 0) > 0)
                            <a href="{{ route('admin.student.reset.violations', ['user_id' => $user->id]) }}"
                               class="btn btn-warning"
                               onclick="return confirm('Bạn có chắc chắn muốn reset số lần vi phạm?')">
                                <i class="fas fa-undo"></i> Reset số lần vi phạm
                            </a>
                            @endif
                        </div>
                    </div>
                    <!-- end buttons -->
                </div>


            </div> <!-- end card-body-->
        </div> <!-- end card -->
    </div> <!-- end col-->
</div>
@endsection

@push('js')
    <script>
        // JavaScript nếu cần
    </script>
@endpush

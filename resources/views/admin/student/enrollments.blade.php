@extends('layouts.admin')
@push('title', get_phrase('Student Enrollments'))
@push('meta')@endpush
@push('css')@endpush
@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    {{ get_phrase('Enrollments for') }} {{ $student->name }}
                </h4>

                <a href="{{ route('admin.student.index') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                    <span class="fi-rr-arrow-left"></span>
                    <span>{{ get_phrase('Back to Students') }}</span>
                </a>
            </div>
        </div>
    </div>
    <div class="ol-card p-4">
        <div class="ol-card-body">

            <div class="row mt-4">
                <div class="col-md-12">
                    <!-- Table -->
                    @if (count($enrollments) > 0)
                        <div class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                            <p class="admin-tInfo">
                                {{ get_phrase('Showing') . ' ' . count($enrollments) . ' ' . get_phrase('of') . ' ' . $enrollments->total() . ' ' . get_phrase('data') }}
                            </p>
                        </div>
                        <div class="table-responsive course_list" id="course_list">
                            <table class="table eTable eTable-2 print-table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">{{ get_phrase('Course') }}</th>
                                        <th scope="col">{{ get_phrase('Enrollment Type') }}</th>
                                        <th scope="col">{{ get_phrase('Entry Date') }}</th>
                                        <th scope="col">{{ get_phrase('Expiry Date') }}</th>
                                        <th class="print-d-none" scope="col">{{ get_phrase('Options') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($enrollments as $key => $row)
                                        <tr>
                                            <th scope="row">
                                                <p class="row-number">{{ ++$key }}</p>
                                            </th>
                                            <td>
                                                <div class="dAdmin_info_name min-w-200px">
                                                    @if ($row->course)
                                                        <p>{{ $row->course->title }}</p>
                                                    @else
                                                        <p class="text-muted">{{ get_phrase('Course not found') }}</p>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                @if ($row->enrollment_type == 'paid')
                                                    <span class="badge bg-success">{{ get_phrase('Paid') }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ $row->enrollment_type ? $row->enrollment_type : get_phrase('Free') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                {{ date('d/m/Y', $row->entry_date) }}
                                            </td>
                                            <td>
                                                @if ($row->expiry_date)
                                                    {{ date('d M Y', $row->expiry_date) }}
                                                @else
                                                    <span class="text-muted">{{ get_phrase('No expiry') }}</span>
                                                @endif
                                            </td>
                                            <td class="print-d-none">
                                                <div class="text-center admin-user-td-btn dropdown">
                                                    <span class="dropend dropend-reverce">
                                                        <button class="btn dropdown-toggle admin-user-btn px-2 py-0" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fi-rr-menu-dots"></i>
                                                        </button>
                                                        <ul class="dropdown-menu admin-user-dropdown" aria-labelledby="dropdownMenuButton1">
                                                            <li>
                                                                <a class="dropdown-item" href="{{ route('admin.enroll.history.delete', $row->id) }}" onclick="return confirm('{{ get_phrase('Are you sure want to delete?') }}')">
                                                                    {{ get_phrase('Delete') }}</a>
                                                            </li>
                                                        </ul>
                                                    </span>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        @include('admin.no_data')
                    @endif

                    <!-- Data info and Pagination -->
                    @if (count($enrollments) > 0)
                        <div class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                            <p class="admin-tInfo">
                                {{ get_phrase('Showing') . ' ' . count($enrollments) . ' ' . get_phrase('of') . ' ' . $enrollments->total() . ' ' . get_phrase('data') }}
                            </p>
                            {{ $enrollments->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

@endsection

@push('js')
@endpush

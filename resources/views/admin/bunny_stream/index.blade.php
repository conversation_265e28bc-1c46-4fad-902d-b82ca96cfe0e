@extends('layouts.admin')

@push('title', get_phrase('Bunny Stream Video Upload'))

@push('meta')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@section('content')

    <style>
        #videoPlayerModalLabel {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%; /* Adjust this value as needed */
        }
        /* Drag & Drop Zone Styling */
        .upload-drop-zone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .upload-drop-zone.highlight {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .upload-drop-zone i {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .upload-drop-zone h4 {
            margin-bottom: 10px;
            color: #495057;
        }

        .upload-drop-zone p {
            color: #6c757d;
        }

        /* File Queue Styling */
        .file-queue {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            background-color: #fff;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
            position: relative;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-item .file-name {
            flex-grow: 1;
            margin-right: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .file-item .file-size {
            color: #6c757d;
            font-size: 0.85rem;
            margin-right: 15px;
            white-space: nowrap;
        }

        .file-item .file-status {
            width: 100px;
            text-align: center;
        }

        .file-item .file-remove {
            margin-left: 10px;
            cursor: pointer;
            color: #dc3545;
        }

        .file-progress {
            height: 5px;
            margin-top: 5px;
            width: 100%;
            background-color: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            position: absolute;
            bottom: 0;
            left: 0;
        }

        .file-progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Form styling */
        #uploadForm label {
            font-weight: 500;
            margin-bottom: 8px;
        }

        #uploadForm .form-control {
            border-radius: 6px;
            border: 1px solid #ced4da;
            padding: 10px 15px;
        }

        #uploadForm .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        #uploadBtn {
            padding: 10px 20px;
            font-weight: 500;
        }

        /* Progress bar styling */
        .progress {
            height: 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            background-color: #e9ecef;
        }

        .progress-bar {
            border-radius: 10px;
            background-color: #007bff;
            font-weight: 500;
        }

        .timer {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }

        /* DataTable styling */
        .video-table-container {
            margin-top: 30px;
        }

        #videoTable {
            width: 100% !important;
            border-collapse: separate;
            border-spacing: 0;
        }

        #videoTable thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            padding: 12px 15px;
            font-weight: 600;
        }

        #videoTable tbody td {
            padding: 12px 15px;
            vertical-align: middle;
        }

        /* Fix DataTable search box and pagination */
        .dataTables_wrapper .dataTables_filter {
            margin-bottom: 15px;
            text-align: right;
        }

        .dataTables_wrapper .dataTables_filter input {
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 6px 12px;
            margin-left: 8px;
        }

        .dataTables_wrapper .dataTables_length {
            margin-bottom: 15px;
        }

        .dataTables_wrapper .dataTables_length select {
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 6px 12px;
        }

        .dataTables_wrapper .dataTables_paginate {
            margin-top: 15px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 5px 10px;
            margin: 0 2px;
            border-radius: 4px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #007bff;
            color: white !important;
            border: 1px solid #007bff;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #e9ecef;
            color: #212529 !important;
            border: 1px solid #dee2e6;
        }

        /* Action buttons styling */
        .action-btn {
            margin-right: 5px;
            padding: 5px 10px;
            font-size: 12px;
        }

        .action-btn i {
            margin-right: 5px;
        }

        /* Video modal styling */
        .video-modal {
            max-width: 800px;
        }

        .video-container {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            height: 0;
            overflow: hidden;
        }

        .video-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }

        /* Alert styling */
        #alertContainer .alert {
            margin-bottom: 20px;
            border-radius: 6px;
        }

        /* Copy tooltip styling */
        .copy-tooltip {
            position: relative;
            display: inline-block;
        }

        .copy-tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .copy-tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .action-btn {
                margin-bottom: 5px;
                display: block;
                width: 100%;
            }

            .dataTables_wrapper .dataTables_filter {
                text-align: left;
                margin-top: 10px;
            }

            .dataTables_wrapper .dataTables_filter input {
                width: 100%;
                margin-left: 0;
            }
        }
    </style>
    <div class="container mt-5">
        <!-- Add Statistics Card -->
        <div class="row justify-content-center mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>{{get_phrase('Storage & Traffic Statistics')}}</span>
                        <button id="refreshStats" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync-alt"></i> {{get_phrase('Refresh')}}
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="stats-item mb-3">
                                    <h5>{{get_phrase('Storage Usage')}}</h5>
                                    <div class="d-flex align-items-center">
                                        <strong id="storageUsage" class="text-20px text-premium">0 MB</strong>
                                    </div>

                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stats-item mb-3">
                                    <h5>{{get_phrase('Traffic Usage')}}</h5>
                                    <div class="d-flex align-items-center">
                                        <strong id="trafficUsage" class="text-20px text-premium">0 MB</strong>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="stats-item">
                                    <h5>{{get_phrase('Summary')}}</h5>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <p class="mb-1">{{get_phrase('Video Count')}}: <span
                                                    id="videoCount">0</span></p>
                                            <p class="mb-1">{{get_phrase('Estimated Monthly Cost')}}: <span
                                                    id="totalCost">$0.00</span></p>
                                        </div>
                                        <div>
                                            <p class="mb-1">{{get_phrase('Library Name')}}: <span
                                                    id="libraryName">-</span></p>
                                            <p class="mb-1">{{get_phrase('Created Date')}}: <span
                                                    id="libraryCreated">-</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <div id="alertContainer"></div>

                        <!-- Drag & Drop Upload Zone -->
                        <div id="dropZone" class="upload-drop-zone">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <h4>{{get_phrase('Drag & Drop Video Files')}}</h4>
                            <p>{{get_phrase('or click to select files')}}</p>
                            <p class="text-muted">{{get_phrase('Maximum size')}}: {{ $maxUploadSize }}</p>
                            <input type="file" id="fileInput" class="d-none" multiple
                                   accept="video/mp4,video/mov,video/avi,video/flv">
                        </div>

                        <!-- File Queue -->
                        <div id="fileQueue" class="file-queue d-none">
                            <h5 class="mb-3">{{get_phrase('Upload Queue')}}</h5>
                            <div id="queueItems"></div>

                            <!-- Upload Status Summary -->
                            <div id="queueSummary" class="mt-3 p-2 bg-light rounded d-none">
                                <div class="d-flex justify-content-between">
                                    <span id="queueProgress">{{get_phrase('Progress')}}: <span id="queueProgressValue">0%</span></span>
                                    <span id="queueStats">0/0 {{get_phrase('files completed')}}</span>
                                </div>
                                <div class="progress mt-2" style="height: 10px;">
                                    <div id="queueProgressBar" class="progress-bar" role="progressbar"
                                         style="width: 0%"></div>
                                </div>
                                <p id="queueStatus"
                                   class="text-center mt-2 mb-0 text-muted small">{{get_phrase('Ready to upload')}}</p>
                            </div>
                        </div>

                        <!-- Upload Controls -->
                        <div class="d-flex justify-content-between mt-3">
                            <button id="clearQueueBtn" class="btn btn-outline-secondary d-none">
                                <i class="fas fa-times"></i> {{get_phrase('Clear Queue')}}
                            </button>
                            <button id="uploadAllBtn" class="btn btn-primary d-none">
                                <i class="fas fa-upload"></i> {{get_phrase('Upload All Videos')}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video List Table -->
        <div class="row video-table-container">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>{{get_phrase('Video list')}}</span>
                        <button id="refreshVideoList" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync-alt"></i> {{get_phrase('Refresh')}}
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="videoTable" class="table table-striped table-hover">
                                <thead>
                                <tr>
                                    <th>{{get_phrase('Title')}}</th>
                                    <th>{{get_phrase('Creation date')}}</th>
                                    <th>{{get_phrase('Views')}}</th>
                                    <th>{{get_phrase('Status')}}</th>
                                    <th>{{get_phrase('Actions')}}</th>
                                </tr>
                                </thead>
                                <tbody>
                                <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Player Modal -->
    <div class="modal fade" id="videoPlayerModal" tabindex="-1" aria-labelledby="videoPlayerModalLabel"
         aria-hidden="true">
        <div class="modal-dialog video-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoPlayerModalLabel">{{get_phrase('Watch video')}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="video-container">
                        <iframe id="videoPlayerIframe" class="video-iframe" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">{{get_phrase('Confirm deletion')}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    {{get_phrase('Are you sure you want to delete the video')}} "<span id="deleteVideoTitle"></span>"?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                            data-bs-dismiss="modal">{{get_phrase('Cancel')}}</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">{{get_phrase('Delete')}}</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // File queue management
        let fileQueue = [];
        const maxUploadSizeBytes = {{ $maxUploadSizeBytes }};
        const maxUploadSizeFormatted = '{{ $maxUploadSize }}';
        let currentlyUploading = false;
        const csrfToken = '{{ csrf_token() }}';

        // Add variables to track queue status
        let queueStatsData = {
            total: 0,
            completed: 0,
            processing: 0,
            failed: 0,
            totalProgress: 0
        };

        // Add variables to track processing videos
        let processingFiles = [];

        // Add variables to track table refresh
        let refreshInterval = null;
        let allVideosReady = false;

        // Initialize drag and drop functionality
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const queueContainer = document.getElementById('fileQueue');
        const queueItems = document.getElementById('queueItems');
        const clearQueueBtn = document.getElementById('clearQueueBtn');
        const uploadAllBtn = document.getElementById('uploadAllBtn');
        const queueSummary = document.getElementById('queueSummary');
        const queueProgressBar = document.getElementById('queueProgressBar');
        const queueProgressValue = document.getElementById('queueProgressValue');
        const queueStatsElement = document.getElementById('queueStats');
        const queueStatus = document.getElementById('queueStatus');

        // Add event listeners for drag and drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('highlight');
        }

        function unhighlight() {
            dropZone.classList.remove('highlight');
        }

        // Handle file drop
        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        // Handle file selection via click
        dropZone.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', () => {
            handleFiles(fileInput.files);
        });

        function handleFiles(files) {
            if (files.length === 0) return;

            // Filter for video files
            const videoFiles = Array.from(files).filter(file =>
                file.type.startsWith('video/') ||
                /\.(mp4|mov|avi|flv)$/i.test(file.name)
            );

            if (videoFiles.length === 0) {
                showAlert('{{get_phrase('Please select valid video files')}}', 'warning');
                return;
            }

            // Check file sizes
            const oversizedFiles = videoFiles.filter(file => file.size > maxUploadSizeBytes);
            if (oversizedFiles.length > 0) {
                const fileNames = oversizedFiles.map(f => f.name).join(', ');
                showAlert(`{{get_phrase('The following files exceed the maximum size limit of')}} ${maxUploadSizeFormatted}: ${fileNames}`, 'warning');

                // Only add files that are within size limit
                const validFiles = videoFiles.filter(file => file.size <= maxUploadSizeBytes);
                addFilesToQueue(validFiles);
            } else {
                addFilesToQueue(videoFiles);
            }

            // Make sure Upload All Videos button appears when new files are added
            const hasPendingFiles = fileQueue.some(f => f.status === 'pending');
            if (hasPendingFiles) {
                uploadAllBtn.classList.remove('d-none');
                uploadAllBtn.disabled = currentlyUploading;
            }
        }

        function addFilesToQueue(files) {
            // Add files to queue
            files.forEach(file => {
                // Check if file is already in queue
                const isDuplicate = fileQueue.some(queuedFile =>
                    queuedFile.name === file.name &&
                    queuedFile.size === file.size &&
                    queuedFile.status !== 'error'
                );

                if (!isDuplicate) {
                    const fileObj = {
                        file: file,
                        id: 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                        name: file.name,
                        size: file.size,
                        status: 'pending', // pending, uploading, processing, complete, error
                        progress: 0,
                        guid: null
                    };

                    fileQueue.push(fileObj);
                    renderFileItem(fileObj);
                }
            });

            updateQueueUI();
            updateQueueSummary();
        }

        function renderFileItem(fileObj) {
            const fileSize = formatFileSize(fileObj.size);
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.id = fileObj.id;
            fileItem.innerHTML = `
                <div class="file-name">${fileObj.name}</div>
                <div class="file-size">${fileSize}</div>
                <div class="file-status">
                    <span class="badge bg-secondary">{{get_phrase('Pending')}}</span>
                </div>
                <div class="file-remove" onclick="removeFile('${fileObj.id}')">
                    <i class="fas fa-times"></i>
                </div>
                <div class="file-progress">
                    <div class="file-progress-bar" style="width: 0%"></div>
                </div>
            `;

            queueItems.appendChild(fileItem);
        }

        function updateFileStatus(fileId, status, progress = null, message = null) {
            const fileObj = fileQueue.find(f => f.id === fileId);
            if (!fileObj) return;

            const oldStatus = fileObj.status;
            fileObj.status = status;

            if (progress !== null) {
                fileObj.progress = progress;
            }

            const fileItem = document.getElementById(fileId);
            if (!fileItem) return;

            const statusElement = fileItem.querySelector('.file-status');
            const progressBar = fileItem.querySelector('.file-progress-bar');

            // Update progress bar if provided
            if (progress !== null) {
                progressBar.style.width = `${progress}%`;
            }

            // Update status badge
            let badgeClass = 'bg-secondary';
            let statusText = '{{get_phrase('Pending')}}';

            switch (status) {
                case 'uploading':
                    badgeClass = 'bg-primary';
                    statusText = '{{get_phrase('Uploading')}}';
                    break;
                case 'processing':
                    badgeClass = 'bg-warning text-dark';
                    statusText = message || '{{get_phrase('Processing')}}';
                    break;
                case 'complete':
                    badgeClass = 'bg-success';
                    statusText = '{{get_phrase('Complete')}}';
                    break;
                case 'error':
                    badgeClass = 'bg-danger';
                    statusText = '{{get_phrase('Error')}}';
                    break;
            }

            statusElement.innerHTML = `<span class="badge ${badgeClass}">${statusText}</span>`;

            // Update queue statistics when status changes
            if (oldStatus !== status) {
                updateQueueSummary();
            }
        }

        function removeFile(fileId) {
            // Find file index in queue
            const fileIndex = fileQueue.findIndex(f => f.id === fileId);
            if (fileIndex === -1) return;

            // Check if file is currently uploading
            if (fileQueue[fileIndex].status === 'uploading') {
                showAlert('{{get_phrase('Cannot remove a file that is currently uploading')}}', 'warning');
                return;
            }

            // Remove from queue array
            fileQueue.splice(fileIndex, 1);

            // Remove from DOM
            const fileItem = document.getElementById(fileId);
            if (fileItem) {
                fileItem.remove();
            }

            updateQueueUI();
            updateQueueSummary();
        }

        function updateQueueUI() {
            if (fileQueue.length > 0) {
                queueContainer.classList.remove('d-none');
                queueSummary.classList.remove('d-none');
                clearQueueBtn.classList.remove('d-none');

                // Only show Upload All Videos button when there are pending files
                const hasPendingFiles = fileQueue.some(f => f.status === 'pending');

                if (hasPendingFiles) {
                    uploadAllBtn.classList.remove('d-none');
                    uploadAllBtn.disabled = currentlyUploading;
                } else {
                    uploadAllBtn.classList.add('d-none');
                }
            } else {
                queueContainer.classList.add('d-none');
                queueSummary.classList.add('d-none');
                clearQueueBtn.classList.add('d-none');
                uploadAllBtn.classList.add('d-none');
            }
        }

        function updateQueueSummary() {
            // Calculate statistics
            const total = fileQueue.length;
            const completed = fileQueue.filter(f => f.status === 'complete').length;
            const processing = fileQueue.filter(f => ['uploading', 'processing'].includes(f.status)).length;
            const failed = fileQueue.filter(f => f.status === 'error').length;
            const pending = fileQueue.filter(f => f.status === 'pending').length;

            // Calculate total progress
            let totalProgress = 0;
            if (total > 0) {
                const progressSum = fileQueue.reduce((sum, file) => {
                    // Ensure file.progress is a number
                    const progress = typeof file.progress === 'number' ? file.progress : 0;
                    return sum + progress;
                }, 0);
                totalProgress = Math.round(progressSum / total);
            }

            // Update UI
            queueProgressBar.style.width = `${totalProgress}%`;
            queueProgressValue.textContent = `${totalProgress}%`;
            queueStatsElement.textContent = `${completed}/${total} {{get_phrase('files completed')}}`;

            // Update status
            if (currentlyUploading) {
                if (processing > 0) {
                    const uploadingCount = fileQueue.filter(f => f.status === 'uploading').length;
                    const processingCount = fileQueue.filter(f => f.status === 'processing').length;

                    if (uploadingCount > 0) {
                        queueStatus.textContent = `{{get_phrase('Uploading')}} ${uploadingCount} {{get_phrase('files')}}...`;
                    } else if (processingCount > 0) {
                        queueStatus.textContent = `{{get_phrase('Processing')}} ${processingCount} {{get_phrase('files')}}...`;
                    } else {
                        queueStatus.textContent = `{{get_phrase('Checking video status')}}...`;
                    }
                } else {
                    queueStatus.textContent = `{{get_phrase('Checking video status')}}...`;
                }
            } else if (completed === total && total > 0) {
                queueStatus.textContent = `{{get_phrase('All files uploaded successfully!')}}`;
            } else if (failed > 0) {
                queueStatus.textContent = `${failed} {{get_phrase('files failed to upload')}}`;
            } else if (pending > 0) {
                queueStatus.textContent = `{{get_phrase('Ready to upload')}} ${pending} {{get_phrase('files')}}`;
            } else {
                queueStatus.textContent = `{{get_phrase('Ready to upload')}}`;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Clear queue button
        clearQueueBtn.addEventListener('click', () => {
            // Check if any file is uploading
            if (fileQueue.some(f => f.status === 'uploading')) {
                showAlert('{{get_phrase('Cannot clear queue while files are uploading')}}', 'warning');
                return;
            }

            fileQueue = [];
            queueItems.innerHTML = '';
            updateQueueUI();
            updateQueueSummary();

            // Reset refresh state
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
            allVideosReady = false;
        });

        // Upload all button
        uploadAllBtn.addEventListener('click', function () {
            if (currentlyUploading) {
                return;
            }

            // If no files are pending, reset the state of all files
            const hasPendingFiles = fileQueue.some(f => f.status === 'pending');
            if (!hasPendingFiles) {
                resetUploadState();
            }

            processQueue();
        });

        function processQueue() {
            if (currentlyUploading) return;

            const pendingFiles = fileQueue.filter(f => f.status === 'pending');
            if (pendingFiles.length === 0) {
                return;
            }

            currentlyUploading = true;
            uploadAllBtn.disabled = true;

            // Display upload start notification
            showAlert(`{{get_phrase('Starting upload of')}} ${pendingFiles.length} {{get_phrase('files')}}...`, 'info');

            // Update queue status
            updateQueueSummary();

            // Start uploading the first file
            uploadFile(pendingFiles[0]);
        }

        function uploadFile(fileObj) {
            updateFileStatus(fileObj.id, 'uploading', 0);

            // Step 1: Create video on Bunny.net to get GUID
            const title = fileObj.name.replace(/\.[^/.]+$/, ""); // Remove extension

            fetch('{{ route('admin.bunny.upload.post') }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: title,
                    step: 'create'
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    fileObj.guid = data.guid;

                    // Step 2: Upload video with received GUID
                    uploadChunkedFile(fileObj);
                })
                .catch(error => {
                    updateFileStatus(fileObj.id, 'error');
                    showAlert(`{{get_phrase('Error creating video')}}: ${error.message}`, 'danger');

                    // Continue with next file
                    continueQueue();
                });
        }

        function uploadChunkedFile(fileObj) {
            const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB chunks
            const file = fileObj.file;
            const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
            let currentChunk = 0;

            function uploadNextChunk() {
                const start = currentChunk * CHUNK_SIZE;
                const end = Math.min(file.size, start + CHUNK_SIZE);
                const chunk = file.slice(start, end);

                const formData = new FormData();
                formData.append('video', chunk, file.name);
                formData.append('guid', fileObj.guid);
                formData.append('_token', csrfToken);
                formData.append('step', 'upload');
                formData.append('chunk', currentChunk);
                formData.append('totalChunks', totalChunks);

                fetch('{{ route('admin.bunny.upload.chunk') }}', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            throw new Error(data.error);
                        }

                        // Update progress
                        currentChunk++;
                        const percentComplete = Math.round((currentChunk / totalChunks) * 100);
                        updateFileStatus(fileObj.id, 'uploading', percentComplete);
                        updateQueueSummary();

                        if (currentChunk < totalChunks) {
                            // Upload next chunk
                            uploadNextChunk();
                        } else {
                            // Completed uploading all chunks
                            updateFileStatus(fileObj.id, 'processing', 100, '{{get_phrase('Finalizing')}}');

                            // Call API to finalize upload
                            finalizeUpload(fileObj);
                        }
                    })
                    .catch(error => {
                        updateFileStatus(fileObj.id, 'error');
                        showAlert(`{{get_phrase('Error uploading chunk')}} ${currentChunk + 1} {{get_phrase('for')}} ${fileObj.name}: ${error.message}`, 'danger');

                        // Continue with next file
                        continueQueue();
                    });
            }

            // Start uploading first chunk
            uploadNextChunk();
        }

        function finalizeUpload(fileObj) {
            fetch('{{ route('admin.bunny.upload.finalize') }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({guid: fileObj.guid})
            })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Update status
                    updateFileStatus(fileObj.id, 'processing', 100, '{{get_phrase('Encoding')}}');

                    // Start checking encoding status
                    checkVideoStatus(fileObj);
                })
                .catch(error => {
                    updateFileStatus(fileObj.id, 'error');
                    showAlert(`{{get_phrase('Error finalizing upload for')}} ${fileObj.name}: ${error.message}`, 'danger');

                    // Continue with next file
                    continueQueue();
                });
        }

        function checkVideoStatus(fileObj) {
            fetch('{{ route('admin.bunny.check-status') }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({guid: fileObj.guid})
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'ready') {
                        // Update status to "Complete" when video is ready
                        updateFileStatus(fileObj.id, 'complete', 100);

                        // Remove file from processing list
                        const processingIndex = processingFiles.indexOf(fileObj.guid);
                        if (processingIndex !== -1) {
                            processingFiles.splice(processingIndex, 1);
                        }

                        showAlert(`{{get_phrase('Video')}} "${fileObj.name}" {{get_phrase('has been processed successfully!')}}`, 'success');

                        // Refresh video list
                        if (videoTable) {
                            videoTable.ajax.reload(null, false);
                        }

                        // Continue with next file
                        continueQueue();

                    } else if (data.status === 'processing') {
                        // Update encoding progress information
                        const progressMessage = data.progress ? `{{get_phrase('Encoding')}} (${data.progress}%)` : '{{get_phrase('Encoding')}}';
                        updateFileStatus(fileObj.id, 'processing', 100, progressMessage);

                        // Check again after 5 seconds
                        setTimeout(() => {
                            checkVideoStatus(fileObj);
                        }, 5000);
                    } else {
                        // If not ready or processing, consider it completed to continue the queue
                        console.log('Video status unknown, continuing queue:', data);
                        updateFileStatus(fileObj.id, 'complete', 100);
                        continueQueue();
                    }
                })
                .catch(error => {
                    console.error('{{get_phrase('Error checking status')}}:', error);

                    // Mark as error and continue with next file
                    updateFileStatus(fileObj.id, 'error');
                    continueQueue();
                });
        }

        function continueQueue() {
            // Update queue status
            updateQueueSummary();

            // Find next file to upload
            const nextFile = fileQueue.find(f => f.status === 'pending');

            if (nextFile) {
                // Upload next file
                uploadFile(nextFile);
            } else {
                // All files completed
                currentlyUploading = false;

                // Update UI - don't show Upload All Videos button again
                // uploadAllBtn will remain disabled until new files are added
                updateQueueUI();
                updateQueueSummary();

                // Check if all files are completed
                const allCompleted = fileQueue.every(f => f.status === 'complete');
                const completedCount = fileQueue.filter(f => f.status === 'complete').length;
                const failedCount = fileQueue.filter(f => f.status === 'error').length;

                // Reload video table immediately
                if (videoTable) {
                    videoTable.ajax.reload();
                }

                if (allCompleted) {
                    showAlert('{{get_phrase('All videos have been uploaded and processed successfully!')}}', 'success');
                    queueStatus.textContent = `{{get_phrase('All files uploaded successfully!')}}`;

                    // Start checking video status every 60 seconds
                    startTableRefresh();
                } else if (failedCount > 0) {
                    showAlert(`{{get_phrase('Upload completed')}}: ${completedCount} {{get_phrase('successful')}}, ${failedCount} {{get_phrase('failed')}}`, 'info');
                    queueStatus.textContent = `{{get_phrase('Upload completed with some errors')}}`;

                    // Start checking video status every 60 seconds
                    startTableRefresh();
                } else {
                    showAlert(`{{get_phrase('All')}} ${completedCount} {{get_phrase('videos uploaded successfully!')}}`, 'success');
                    queueStatus.textContent = `{{get_phrase('All files uploaded successfully!')}}`;

                    // Start checking video status every 60 seconds
                    startTableRefresh();
                }
            }
        }

        // Thêm hàm showAlert nếu chưa có
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            `;
        }

        // Khởi tạo DataTable
        let videoTable;
        $(document).ready(function () {
            videoTable = $('#videoTable').DataTable({
                processing: true,
                serverSide: false,
                pageLength: 20,
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/vi.json'
                },
                ajax: {
                    url: '{{ route('admin.bunny.list') }}',
                    dataSrc: 'items'
                },
                columns: [
                    {
                        data: 'title',
                        render: function (data, type, row) {
                            if (data) {
                                // If title has more than a certain number of characters, truncate and add "..."
                                const maxCharacters = 50; // You can adjust this number as needed

                                if (data.length > maxCharacters) {
                                    return data.substring(0, maxCharacters) + '...';
                                } else {
                                    return data;
                                }
                            } else {
                                return '<em>{{get_phrase('No title')}}</em>';
                            }
                        }
                    },
                    {
                        data: 'dateUploaded',
                        render: function (data) {
                            // Chuyển đổi chuỗi ngày tháng thành đối tượng Date
                            const date = new Date(data);

                            // Lấy ngày, tháng, năm
                            const day = date.getDate().toString().padStart(2, '0');
                            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Tháng bắt đầu từ 0
                            const year = date.getFullYear();

                            // Trả về chuỗi định dạng dd/mm/yyyy
                            return `${day}/${month}/${year}`;
                        }
                    },
                    {data: 'views'},
                    {
                        data: 'status',
                        render: function (data) {
                            switch (data) {
                                case 0:
                                    return '<span class="badge bg-secondary">{{get_phrase('Pending')}}</span>';
                                case 3:
                                    return '<span class="badge bg-warning text-dark">{{get_phrase('Processing')}}</span>';
                                case 4:
                                    return '<span class="badge bg-success">{{get_phrase('Ready')}}</span>';
                                case 5:
                                    return '<span class="badge bg-danger">{{get_phrase('Error')}}</span>';
                                default:
                                    return '<span class="badge bg-info">{{get_phrase('Processing')}}</span>';
                            }
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            let buttons = '';

                            // View button only shows when video is ready
                            if (row.status === 4) {
                                buttons += `<button class="btn btn-sm btn-primary action-btn view-video me-1" data-guid="${row.guid}" data-title="${row.title || 'Video'}">
                                <i class="fas fa-play"></i> {{get_phrase('Watch')}}
                                </button>`;

                                // Add copy embed link button
                                buttons += `<button class="btn btn-sm btn-info action-btn copy-embed me-1" data-guid="${row.guid}">
                                <i class="fas fa-code"></i> {{get_phrase('Copy')}}
                                </button>`;
                            }

                            buttons += `<button class="btn btn-sm btn-danger action-btn delete-video" data-guid="${row.guid}" data-title="${row.title || '{{get_phrase('Video with no title')}}'}">
                            <i class="fas fa-trash"></i> {{get_phrase('Delete')}}
                            </button>`;

                            return buttons;
                        }
                    }
                ],
                order: [[1, 'desc']] // Sort by newest creation date
            });

            // Refresh video list
            $('#refreshVideoList').on('click', function () {
                videoTable.ajax.reload();
            });

            // Thêm lại các sự kiện xử lý cho các nút

            // Handle watch video button
            $('#videoTable').on('click', '.view-video', function () {
                const guid = $(this).data('guid');
                const title = $(this).data('title');
                const iframeSrc = `{{$url_media_delivery}}/embed/{{$libraryId}}/${guid}`;

                $('#videoPlayerModalLabel').text(title);
                $('#videoPlayerIframe').attr('src', iframeSrc);

                const videoPlayerModal = new bootstrap.Modal(document.getElementById('videoPlayerModal'));
                videoPlayerModal.show();
            });

            // Handle copy embed link button
            $('#videoTable').on('click', '.copy-embed', function () {
                const guid = $(this).data('guid');
                const embedLink = `{{$url_media_delivery}}/embed/{{$libraryId}}/${guid}`;

                // Create a temporary input to copy
                const tempInput = document.createElement('input');
                tempInput.value = embedLink;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);

                // Show alert notification
                showAlert('{{get_phrase('Video embed link copied to clipboard!')}}', 'success');
            });

            // Handle delete video button
            $('#videoTable').on('click', '.delete-video', function () {
                const guid = $(this).data('guid');
                const title = $(this).data('title');

                $('#deleteVideoTitle').text(title);
                $('#confirmDeleteBtn').data('guid', guid);

                const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                deleteConfirmModal.show();
            });

            // Confirm delete video
            $('#confirmDeleteBtn').on('click', function () {
                const guid = $(this).data('guid');

                $.ajax({
                    url: '{{ route('admin.bunny.delete') }}',
                    type: 'POST',
                    data: {
                        guid: guid,
                        _token: csrfToken
                    },
                    beforeSend: function () {
                        $('#confirmDeleteBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> {{get_phrase('Deleting...')}}');
                    },
                    success: function (response) {
                        bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
                        showAlert('{{get_phrase('Video has been successfully deleted')}}', 'success');
                        videoTable.ajax.reload();
                    },
                    error: function (xhr) {
                        let errorMessage = '{{get_phrase('An error occurred while deleting the video')}}';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        }
                        showAlert(errorMessage, 'danger');
                    },
                    complete: function () {
                        $('#confirmDeleteBtn').prop('disabled', false).text('{{get_phrase('Delete')}}');
                    }
                });
            });

            // Close video player when modal is closed
            $('#videoPlayerModal').on('hidden.bs.modal', function () {
                $('#videoPlayerIframe').attr('src', '');
            });

            // Thêm sự kiện khi bảng được làm mới
            videoTable.on('draw', function () {
                checkAllVideosReady();
            });
        });


        function startTableRefresh() {
            // Clear old interval if exists
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }

            // Set up new interval
            refreshInterval = setInterval(() => {
                // Check status of processing videos
                checkVideoStatusPeriodically();
            }, 10000); // 10 seconds
        }


        function checkAllVideosReady() {
            // If no videos are processing, stop refreshing
            if (allVideosReady) {
                return;
            }

            // Get data from table
            const tableData = videoTable.data();

            // Check if all videos are ready
            let allReady = true;
            for (let i = 0; i < tableData.length; i++) {
                const row = tableData[i];
                if (row.status !== 4) { // 4 is Ready status
                    allReady = false;
                    break;
                }
            }

            // If all videos are ready, stop refreshing
            if (allReady && tableData.length > 0) {
                allVideosReady = true;
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                    refreshInterval = null;
                    console.log('All videos are ready, stopped auto-refresh');
                }
            }
        }


        function resetUploadState() {
            currentlyUploading = false;
            uploadAllBtn.disabled = false;

            // Mark all completed files as pending to allow re-upload
            fileQueue.forEach(file => {
                if (file.status === 'complete' || file.status === 'error') {
                    file.status = 'pending';
                    file.progress = 0;
                    updateFileStatus(file.id, 'pending', 0);
                }
            });

            updateQueueUI();
            updateQueueSummary();
        }


        function checkVideoStatusPeriodically() {
            // If no videos are processing, stop checking
            if (allVideosReady) {
                return;
            }

            // Get data from table
            if (videoTable) {
                videoTable.ajax.reload(function () {
                    // Callback after refreshing table
                    checkAllVideosReady();
                }, false);
            }
        }

        // Add statistics functionality
        $(document).ready(function () {
            // Load statistics on page load
            loadLibraryStatistics();

            // Refresh statistics button
            $('#refreshStats').on('click', function () {
                loadLibraryStatistics();
            });

            function loadLibraryStatistics() {
                // Show loading state
                $('#refreshStats').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');

                $.ajax({
                    url: '{{ route('admin.bunny.statistics') }}',
                    type: 'GET',
                    success: function (response) {
                        updateStatisticsUI(response);
                    },
                    error: function (xhr) {
                        let errorMessage = '{{get_phrase('Failed to load statistics')}}';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        }
                        showAlert(errorMessage, 'danger');
                    },
                    complete: function () {
                        $('#refreshStats').prop('disabled', false).html('<i class="fas fa-sync-alt"></i> {{get_phrase('Refresh')}}');
                    }
                });
            }

            function updateStatisticsUI(data) {
                // Format storage usage
                const storageUsage = formatBytes(data.StorageUsage);
                $('#storageUsage').text(storageUsage);

                // Format traffic usage
                const trafficUsage = formatBytes(data.TrafficUsage);
                $('#trafficUsage').text(trafficUsage);

                // Calculate costs
                // Storage: $0.01 per GB per month, doubled for replication
                const storageGB = data.StorageUsage / 1000000000;


                const storageCost = (storageGB * 0.01 * 2);


                // Traffic: $5 per TB
                const trafficTB = data.TrafficUsage / 1000000000;

                const trafficCost = trafficTB * 0.005;

                // Total cost
                const totalCost = (parseFloat(storageCost) + parseFloat(trafficCost)).toFixed(2);

                // Calculate VND equivalent (exchange rate: 1 USD = 26,000 VND)
                const exchangeRate = 26000;
                const totalCostVND = Math.round(totalCost * exchangeRate).toLocaleString('vi-VN');

                // Display both USD and VND
                $('#totalCost').html(`$${totalCost} <span class="text-muted">(≈ ${totalCostVND} đ)</span>`);

                // Set progress bars (assuming 1TB as max for visualization)
                const maxStorage = 1024 * 1024 * 1024 * 1024; // 1TB in bytes
                const storagePercentage = Math.min(100, (data.StorageUsage / maxStorage) * 100);
                $('#storageProgressBar').css('width', storagePercentage + '%');

                const trafficPercentage = Math.min(100, (data.TrafficUsage / maxStorage) * 100);
                $('#trafficProgressBar').css('width', trafficPercentage + '%');

                // Other library info
                $('#videoCount').text(data.VideoCount);
                $('#libraryName').text(data.Name);

                // Format date
                const createdDate = new Date(data.DateCreated);
                const formattedDate = `${createdDate.getDate().toString().padStart(2, '0')}/${(createdDate.getMonth() + 1).toString().padStart(2, '0')}/${createdDate.getFullYear()}`;
                $('#libraryCreated').text(formattedDate);
            }

            function formatBytes(bytes, decimals = 2) {
                if (bytes === 0) return '0 Bytes';

                const k = 1000; // Using 1000 for decimal conversion (SI units)
                const dm = decimals < 0 ? 0 : decimals;

                // First convert to MB
                const bytesInMB = bytes / 1000000;

                // If it's less than 1000 MB, return in MB
                if (bytesInMB < 1000) {
                    return parseFloat(bytesInMB.toFixed(dm)) + ' MB';
                }
                // Otherwise, return in GB
                else {
                    const bytesInGB = bytesInMB / 1000;
                    return parseFloat(bytesInGB.toFixed(dm)) + ' GB';
                }
            }
        });
    </script>

@endsection

@php
use Illuminate\Support\Facades\Auth;
@endphp

@extends('layouts.admin')

@push('title', get_phrase('Addons'))

@push('meta')
<meta name="csrf-token" content="{{ csrf_token() }}" />
@endpush

@push('css')
<style>
/* Add top spacing for content */
.addons-wrapper {
    padding: 30px 0;
}

.addon-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    height: 100%;
    position: relative;
}

.addon-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.addon-status-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #dc3545;
}

.addon-status-indicator.active {
    background-color: #28a745;
}

.addon-header {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 15px;
}

.addon-icon {
    width: 100px;
    height: 100px;
    border-radius: 4px;
    object-fit: cover;
    background-color: #f0f0f0;
}

.addon-info h4 {
    margin: 0 0 10px;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
}

.addon-meta {
    color: #646970;
    font-size: 12px;
    margin-bottom: 8px;
}

.addon-meta i {
    margin-right: 5px;
}

.addon-description {
    color: #50575e;
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 15px;
    height: 78px;
    overflow: hidden;
}

.addon-permissions {
    margin-top: 15px;
    margin-bottom: 15px;
}

.addon-permissions h5 {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1d2327;
}

.permission-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.permission-tag {
    background-color: #f0f4f9;
    color: #2271b1;
    border-radius: 4px;
    padding: 3px 8px;
    font-size: 11px;
    font-weight: 500;
}

.addon-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 15px;
    border-top: 1px solid #ddd;
    flex-wrap: wrap;
    gap: 10px;
}

.addon-status {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.addon-status .badge {
    padding: 5px 10px;
    font-size: 11px;
    font-weight: 500;
}

.badge-pro {
    background-color: #FF5722;
    color: white;
}

.addon-actions .btn {
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 500;
    border-radius: 4px;
}

.addon-actions .btn-primary {
    background: #2271b1;
    border-color: #2271b1;
}

.addon-actions .btn-primary:hover {
    background: #135e96;
    border-color: #135e96;
}

.btn-deactivate {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-deactivate:hover {
    background: #bb2d3b;
    border-color: #bb2d3b;
    color: white;
}

/* Add styles for package badges */
.package-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-right: 5px;
    text-align: center;
    min-width: 110px;
}

.package-badge.free {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
}

.package-badge.pro {
    background-color: #fff8e1;
    color: #ff8f00;
    border: 1px solid #ffe082;
}

.package-badge.premium {
    background-color: #fce4ec;
    color: #c2185b;
    border: 1px solid #f8bbd0;
}

/* Price badge styles */
.price-badge {
    display: inline-block;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
    letter-spacing: 0.3px;
}

.price-badge.mien-phi {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.price-badge.co-phi {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
}

/* Custom Toast Styling */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.custom-toast {
    min-width: 300px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    overflow: hidden;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    margin-bottom: 15px;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    border-left: 4px solid #2271b1;
}

.custom-toast.show {
    opacity: 1;
    transform: translateY(0);
}

.custom-toast .toast-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #fff;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.custom-toast .toast-body {
    padding: 12px 15px;
    color: #444;
    font-size: 14px;
    line-height: 1.5;
}

.custom-toast .toast-title {
    font-weight: 600;
    color: #333;
    margin-right: auto;
    font-size: 15px;
    display: flex;
    align-items: center;
}

.custom-toast .toast-title i {
    margin-right: 8px;
    font-size: 18px;
}

.custom-toast .close-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #888;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.custom-toast .close-btn:hover {
    background-color: rgba(0,0,0,0.1);
    color: #555;
}

.custom-toast.success {
    border-left-color: #28a745;
}

.custom-toast.success .toast-title i {
    color: #28a745;
}

.custom-toast.error {
    border-left-color: #dc3545;
}

.custom-toast.error .toast-title i {
    color: #dc3545;
}

.addon-permissions small.text-muted {
    font-size: 11px;
    color: #6c757d;
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.addon-permissions .fi-rr-info-circle {
    font-size: 14px;
    margin-right: 5px;
    color: #2271b1;
}

/* Purchased indicator styles */
.purchased-badge {
    position: absolute;
    top: 10px;
    right: 30px; /* Position to the left of the status indicator */
    background-color: #4caf50;
    color: white;
    font-size: 10px;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
</style>
@endpush

@section('content')
<div class="addons-wrapper">
    <div class="row g-3">
        @if(count($addons) == 0)
            <div class="col-12 text-center">
                <div class="alert alert-info">
                    {{ get_phrase('No addons found. Please seed some addons first.') }}
                    <a href="{{ route('admin.addons.seed') }}" class="btn btn-sm btn-primary ms-2">{{ get_phrase('Seed Addons') }}</a>
                </div>
            </div>
        @endif

        @foreach($addons as $addon)
        <div class="col-md-4">
            <div class="addon-card">
                <div class="addon-status-indicator {{ $addon->status ? 'active' : '' }}"
                     title="{{ $addon->status ? get_phrase('Active') : get_phrase('Inactive') }}"></div>
                @if($addon->is_purchased && $addon->price > 0)
                    <div class="purchased-badge">Đã mua</div>
                @endif
                <div class="addon-header">
                    <img src="{{ !empty($addon->thumbnail) ? $addon->thumbnail : App\Http\Controllers\Admin\AddonsController::getDefaultThumbnail() }}"
                         alt="{{ $addon->name }}"
                         class="addon-icon">
                    <div class="addon-info">
                        <h4>
                            {{ get_phrase($addon->name) }}
                            <span class="price-badge {{ $addon->price == 0 ? 'mien-phi' : 'co-phi' }}">
                                {{ $addon->formatted_price }}
                            </span>
                        </h4>
                        <div class="addon-meta">
                            <span><i class="fi fi-rr-user"></i> {{ get_phrase('By ' . $addon->author) }}</span>
                        </div>
                        <div class="addon-description">
                            {{ get_phrase($addon->description) }}
                        </div>
                    </div>
                </div>

{{--                @if(!empty($addon->permission_keys))--}}
{{--                <div class="addon-permissions">--}}
{{--                    <h5>{{ get_phrase('Permissions to be granted') }}</h5>--}}
{{--                    <div class="permission-tags">--}}
{{--                        @foreach($addon->permission_keys as $permission)--}}
{{--                            <span class="permission-tag">{{ get_phrase(str_replace('admin.', '', $permission)) }}</span>--}}
{{--                        @endforeach--}}
{{--                    </div>--}}
{{--                    <small class="text-muted mt-2 d-block">--}}
{{--                        <i class="fi fi-rr-info-circle me-1"></i>--}}
{{--                        {{ $addon->status ? --}}
{{--                           get_phrase('These permissions are currently active for your account.') : --}}
{{--                           get_phrase('These permissions will be added to your account when activated.') }}--}}
{{--                    </small>--}}
{{--                </div>--}}
{{--                @endif--}}

                <div class="addon-footer">
                    <div class="addon-status">
                        <span class="package-badge {{ strtolower($addon->required_package) }}">
                            Yêu cầu gói:
                            @if($addon->required_package == 'FREE')
                              {{get_phrase('FREE')}}
                            @elseif($addon->required_package == 'PRO')
                                {{get_phrase('PRO')}}
                            @elseif($addon->required_package == 'PREMIUM')
                                {{get_phrase('PREMIUM')}}
                            @else
                                {{ $addon->required_package }}
                            @endif
                        </span>
                    </div>
                    <div class="addon-actions">
                        <button class="btn {{ $addon->status ? 'btn-deactivate' : 'btn-primary' }} toggle-addon"
                                data-id="{{ $addon->id }}"
                                data-status="{{ $addon->status }}">
                            {{ $addon->status ? get_phrase('Deactivate') : get_phrase('Activate') }}
                        </button>
                        
                        @if($addon->price > 0 && is_root_admin(Auth::id()))
                        <button class="btn btn-sm {{ $addon->is_purchased ? 'btn-outline-success' : 'btn-outline-primary' }} toggle-purchased ms-2"
                                data-id="{{ $addon->id }}"
                                data-purchased="{{ $addon->is_purchased }}">
                            {{ $addon->is_purchased ? 'Đã mua' : 'Đánh dấu đã mua' }}
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>

<!-- Toast container for notifications -->
<div class="toast-container"></div>
@endsection

@push('js')
<script>
// Set up CSRF token for all AJAX requests
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

$(document).ready(function() {
    console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
    
    // Toggle addon status
    $('.toggle-addon').on('click', function() {
        var button = $(this);
        var addonId = button.data('id');
        var statusIndicator = button.closest('.addon-card').find('.addon-status-indicator');

        console.log('Toggle button clicked for addon ID:', addonId);
        console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
        console.log('Request URL:', '{{ url("admin/addons/toggle-status") }}/' + addonId);

        $.ajax({
            url: '{{ url("admin/addons/toggle-status") }}/' + addonId,
            type: 'POST',
            beforeSend: function(xhr) {
                console.log('Request is being sent...');
                // Disable button while processing
                button.prop('disabled', true);
            },
            success: function(response) {
                console.log('Response received:', response);
                button.prop('disabled', false);

                if (response.status) {
                    // Update button state
                    if (response.active) {
                        button.removeClass('btn-primary').addClass('btn-deactivate');
                        button.text('{{ get_phrase("Deactivate") }}');
                        statusIndicator.addClass('active').attr('title', '{{ get_phrase("Active") }}');
                        // Update permission message
                        var permissionInfo = button.closest('.addon-card').find('.text-muted');
                        if (permissionInfo.length) {
                            permissionInfo.html('<i class="fi fi-rr-info-circle me-1"></i>{{ get_phrase("These permissions are currently active for your account.") }}');
                        }
                        // Show success message
                        showToast('{{ get_phrase("Addon activated successfully. Necessary permissions have been granted to your account.") }}', 'success');
                    } else {
                        button.removeClass('btn-deactivate').addClass('btn-primary');
                        button.text('{{ get_phrase("Activate") }}');
                        statusIndicator.removeClass('active').attr('title', '{{ get_phrase("Inactive") }}');
                        // Update permission message
                        var permissionInfo = button.closest('.addon-card').find('.text-muted');
                        if (permissionInfo.length) {
                            permissionInfo.html('<i class="fi fi-rr-info-circle me-1"></i>{{ get_phrase("These permissions will be added to your account when activated.") }}');
                        }
                        // Show success message
                        showToast('{{ get_phrase("Addon deactivated successfully. Associated permissions have been removed from your account.") }}', 'success');
                    }
                    button.data('status', response.active ? 1 : 0);
                } else {
                    // Show error message
                    showToast(response.message, 'error');

                    // If it's a package limitation, show upgrade info
                    if (response.message.includes('upgrade to PRO') || response.message.includes('Bạn cần nâng cấp')) {
                        showUpgradeModal(response.message);
                    }
                    
                    // If it's about contacting admin for paid addon
                    if (response.message.includes('Tiện ích này có phí')) {
                        showContactAdminModal(response.message);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error("AJAX Error:", error);
                console.error("Status:", status);
                console.error("Response:", xhr.responseText);
                console.error("Status Code:", xhr.status);
                button.prop('disabled', false);
                showToast('{{ get_phrase("An error occurred while processing your request.") }}', 'error');
            }
        });
    });
    
    // Toggle purchased status
    $('.toggle-purchased').on('click', function() {
        var button = $(this);
        var addonId = button.data('id');
        
        console.log('Toggle purchased clicked for addon ID:', addonId);
        
        $.ajax({
            url: '{{ url("admin/addons/toggle-purchased") }}/' + addonId,
            type: 'POST',
            beforeSend: function(xhr) {
                // Disable button while processing
                button.prop('disabled', true);
            },
            success: function(response) {
                console.log('Response received:', response);
                button.prop('disabled', false);
                
                if (response.status) {
                    if (response.purchased) {
                        // Update to purchased state
                        button.removeClass('btn-outline-primary').addClass('btn-outline-success');
                        button.text('Đã mua');
                        
                        // Add purchased badge if it doesn't exist
                        var addonCard = button.closest('.addon-card');
                        if (addonCard.find('.purchased-badge').length === 0) {
                            addonCard.prepend('<div class="purchased-badge">Đã mua</div>');
                        }
                    } else {
                        // Update to not purchased state
                        button.removeClass('btn-outline-success').addClass('btn-outline-primary');
                        button.text('Đánh dấu đã mua');
                        
                        // Remove purchased badge
                        button.closest('.addon-card').find('.purchased-badge').remove();
                    }
                    
                    button.data('purchased', response.purchased ? 1 : 0);
                    showToast(response.message, 'success');
                    
                    // Reload the page if needed to refresh the state
                    // window.location.reload();
                } else {
                    showToast(response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error("AJAX Error:", error);
                button.prop('disabled', false);
                showToast('Đã xảy ra lỗi khi xử lý yêu cầu của bạn.', 'error');
            }
        });
    });

    // Function to show upgrade modal
    function showUpgradeModal(message) {
        // Create upgrade modal HTML
        var modalHtml = `
            <div class="modal fade" id="upgradeModal" tabindex="-1" aria-labelledby="upgradeModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="upgradeModalLabel">Yêu cầu nâng cấp gói</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="fi fi-sr-crown text-warning" style="font-size: 48px;"></i>
                            </div>
                            <p>${message}</p>
                            <p>Vui lòng nâng cấp gói dịch vụ của bạn để sử dụng tính năng này.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                            <a href="https://topid.vn" target="_blank" class="btn btn-warning">Xem các gói dịch vụ</a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#upgradeModal').remove();

        // Append modal to body
        $('body').append(modalHtml);

        // Show the modal
        var upgradeModal = new bootstrap.Modal(document.getElementById('upgradeModal'));
        upgradeModal.show();
    }

    // Function to show contact admin modal for paid addons
    function showContactAdminModal(message) {
        // Create contact modal HTML
        var modalHtml = `
            <div class="modal fade" id="contactAdminModal" tabindex="-1" aria-labelledby="contactAdminModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="contactAdminModalLabel">Yêu cầu mua tiện ích</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="fi fi-sr-shop text-primary" style="font-size: 48px;"></i>
                            </div>
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                            <a href="https://topid.vn" target="_blank" class="btn btn-primary">Đến TOPID.VN</a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#contactAdminModal').remove();

        // Append modal to body
        $('body').append(modalHtml);

        // Show the modal
        var contactModal = new bootstrap.Modal(document.getElementById('contactAdminModal'));
        contactModal.show();
    }

    // Function to show custom toast notifications
    function showToast(message, type) {
        var icon = type === 'success' ? 'fi fi-sr-check-circle' : 'fi fi-sr-exclamation-triangle';
        var title = type === 'success' ? '{{ get_phrase("Success") }}' : '{{ get_phrase("Error") }}';

        var toastId = 'toast-' + Date.now();
        var toastHtml = `
            <div id="${toastId}" class="custom-toast ${type}">
                <div class="toast-header">
                    <div class="toast-title">
                        <i class="${icon}"></i>
                        ${title}
                    </div>
                    <button type="button" class="close-btn">
                        <i class="fi fi-sr-cross-small"></i>
                    </button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        $('.toast-container').append(toastHtml);
        var toastElement = $('#' + toastId);

        // Trigger reflow to enable transitions
        toastElement[0].offsetHeight;

        // Show the toast
        toastElement.addClass('show');

        // Set up the close button
        toastElement.find('.close-btn').on('click', function() {
            removeToast(toastId);
        });

        // Auto-hide the toast after 5 seconds
        setTimeout(function() {
            removeToast(toastId);
        }, 5000);
    }

    // Function to remove a toast with animation
    function removeToast(toastId) {
        var toastElement = $('#' + toastId);
        toastElement.removeClass('show');

        // Remove the element after the transition completes
        setTimeout(function() {
            toastElement.remove();
        }, 300);
    }
});
</script>
@endpush

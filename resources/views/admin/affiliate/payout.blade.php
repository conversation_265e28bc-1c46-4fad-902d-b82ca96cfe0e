@extends('layouts.admin')
@push('title', get_phrase('Withdraw Affiliate'))
@push('meta')@endpush
@push('css')@endpush
@section('content')

    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-4 px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    {{ get_phrase('Withdraw Affiliate') }}
                </h4>
            </div>
        </div>
    </div>

    <div class="ol-card p-4">
        <div class="ol-card-body">

            <div class="row mt-4">
                <div class="col-md-12">
                    <ul class="nav nav-tabs eNav-Tabs-custom eTab" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a href="{{ route('admin.affiliate.withdraws', ['active_tab' => 'cHome']) }}"
                                class="nav-link @if($active_tab == 'cHome') show active @endif"
                                id="cHome-tab" role="tab"
                                aria-controls="cHome" aria-selected="true">
                                {{ get_phrase('Pending payouts') }}
                                <span></span>
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a href="{{ route('admin.affiliate.withdraws', ['active_tab' => 'cProfile']) }}" 
                                class="nav-link @if($active_tab == 'cProfile') show active @endif"
                                id="cProfile-tab" role="tab"
                                aria-controls="cProfile" aria-selected="false">
                                {{ get_phrase('Completed payouts') }}
                                <span></span>
                            </a>
                        </li>

                        <li class="nav-item" role="presentation">
                            <a href="{{ route('admin.affiliate.withdraws', ['active_tab' => 'cCanceled']) }}"
                                class="nav-link @if($active_tab == 'cCanceled') show active @endif"
                                id="cCanceled-tab" role="tab"
                                aria-controls="cCanceled" aria-selected="false">
                                {{ get_phrase('Canceled payouts') }}
                                <span></span>
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content eNav-Tabs-content" id="myTabContent">
                        <div class="tab-pane fade @if($active_tab == 'cHome') show active @endif"
                            id="cHome" role="tabpanel" aria-labelledby="cHome-tab">
                            <div class="row print-d-none mt-4 row-gap-3">
                                <div class="col-md-6 pt-2 pt-md-0">
                                    @if (count($affiliate_payout_pending) > 0)
                                        <div class="custom-dropdown">
                                            <button class="dropdown-header btn ol-btn-light">
                                                {{ get_phrase('Export') }}
                                                <i class="fi-rr-file-export ms-2"></i>
                                            </button>
                                            <ul class="dropdown-list">
                                                <li>
                                                    <a class="dropdown-item" href="#"
                                                        onclick="downloadPDF('.print-table', 'payout-request')"><i
                                                            class="fi-rr-file-pdf"></i> {{ get_phrase('PDF') }}</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="window.print();"><i
                                                            class="fi-rr-print"></i> {{ get_phrase('Print') }}</a>
                                                </li>
                                            </ul>
                                        </div>
                                    @endif
                                </div>

                                <div class="col-md-6">
                                    <form class="form-inline" action="{{ route('admin.affiliate.withdraws') }}"
                                        method="get">
                                        <div class="row mb-4">
                                            <div class="col-md-9">
                                                <div class="mb-3 position-relative position-relative">
                                                    <input type="text"
                                                        class="form-control ol-form-control daterangepicker w-100"
                                                        name="eDateRangecHome" value="{{ $tab_date_ranges['cHome'] }}" />
                                                    <input type="hidden" name="active_tab" value="cHome">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <button type="submit" class="btn ol-btn-primary w-100" id="submit-button"
                                                    onclick="update_date_range();"> {{ get_phrase('Filter') }}</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            @if ($affiliate_payout_pending->total() > 0)
                                <div
                                    class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                                    <p class="admin-tInfo">
                                        {{ get_phrase('Showing') . ' ' . count($affiliate_payout_pending) . ' ' . get_phrase('of') . ' ' . $affiliate_payout_pending->total() . ' ' . get_phrase('data') }}
                                    </p>
                                    {{ $affiliate_payout_pending->appends(request()->query())->links() }}
                                </div>
                                <div class="table-responsive purchase_list mt-4" id="purchase_list">
                                    <table class="table eTable eTable-2 print-table">
                                        <thead>
                                            <tr>
                                                <th scope="col">#</th>
                                                <th scope="col">{{ get_phrase('Name') }}</th>
                                                <th scope="col">{{ get_phrase('Payout amount') }}</th>
                                                <th scope="col">{{ get_phrase('Request date') }}</th>
                                                <th scope="col">{{ get_phrase('Bank Info') }}</th>
                                                <th scope="col" class="print-d-none">{{ get_phrase('Action') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($affiliate_payout_pending as $key => $payout_pending)
                                                <tr>
                                                    <th scope="row">
                                                        <p class="row-number">{{ ++$key }}</p>
                                                    </th>
                                                    <td>
                                                        <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                            <div class="dAdmin_profile_img">
                                                                <img class="img-fluid rounded-circle image-45" width="45"
                                                                    height="45"
                                                                    src="{{ get_image($payout_pending->user->photo) }}" />
                                                            </div>
                                                            <div class="ms-1">
                                                                <h4 class="title fs-14px">
                                                                    {{ $payout_pending->user->name }}</h4>
                                                                <p class="sub-title2 text-12px">
                                                                    {{ $payout_pending->user->email }}</p>
                                                            </div>
                                                        </div>
                                                    </td>

                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            <p>{{ currency($payout_pending->amount) }}</p>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            <p>
                                                                @if(is_string($payout_pending->request_date))
                                                                    {{ $payout_pending->request_date }}
                                                                @elseif($payout_pending->request_date instanceof \DateTime || $payout_pending->request_date instanceof \Carbon\Carbon)
                                                                    {{ $payout_pending->request_date->format('d/m/Y') }}
                                                                @else
                                                                    N/A
                                                                @endif
                                                            </p>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            @if(!empty($payout_pending->note))
                                                                <small>{!! nl2br(e($payout_pending->note)) !!}</small>
                                                            @else
                                                                <small class="text-muted">{{ get_phrase('No bank info') }}</small>
                                                            @endif
                                                        </div>
                                                    </td>

                                                    <td class="print-d-none">
                                                        <div class="d-flex gap-2">
                                                            <form
                                                                action="{{ route('admin.affiliate.withdraw.status', [$payout_pending->id, 1]) }}"
                                                                method="post">
                                                                @csrf
                                                                <button type="submit" class="btn btn-success btn-sm">
                                                                    <i class="fi-rr-check"></i>
                                                                    {{ get_phrase('Completed') }}</button>
                                                            </form>
                                                            <form
                                                                action="{{ route('admin.affiliate.withdraw.status', [$payout_pending->id, 2]) }}"
                                                                method="post">
                                                                @csrf
                                                                <button type="submit" class="btn btn-danger btn-sm">
                                                                    <i class="fi-rr-ban"></i>
                                                                    {{ get_phrase('Canceled') }}</button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr>
                                                <th></th>
                                                <th></th>
                                                <th>{{ get_phrase('Total') }} :
                                                    {{ currency($affiliate_payout_pending_total_amount) }}
                                                </th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                @include('admin.no_data')
                            @endif
                        </div>
                        <div class="tab-pane fade @if($active_tab == 'cProfile') show active @endif" id="cProfile"
                            role="tabpanel" aria-labelledby="cProfile-tab">

                            <div class="row print-d-none mt-4 row-gap-3">
                                <div class="col-md-6 pt-2 pt-md-0">
                                    @if (count($affiliate_payout_complete) > 0)
                                        <div class="custom-dropdown">
                                            <button class="dropdown-header btn ol-btn-light">
                                                {{ get_phrase('Export') }}
                                                <i class="fi-rr-file-export ms-2"></i>
                                            </button>
                                            <ul class="dropdown-list">
                                                <li>
                                                    <a class="dropdown-item" href="#"
                                                        onclick="downloadPDF('.print-table', 'accepted-payout')"><i
                                                            class="fi-rr-file-pdf"></i> {{ get_phrase('PDF') }}</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="window.print();"><i
                                                            class="fi-rr-print"></i> {{ get_phrase('Print') }}</a>
                                                </li>
                                            </ul>
                                        </div>
                                    @endif
                                </div>

                                <div class="col-md-6">
                                    <form class="form-inline" action="{{ route('admin.affiliate.withdraws') }}"
                                        method="get">
                                        <div class="row mb-4">
                                            <div class="col-md-9">
                                                <div class="mb-3 position-relative position-relative">
                                                    <input type="text"
                                                        class="form-control ol-form-control daterangepicker w-100"
                                                        name="eDateRangecProfile"
                                                        value="{{ $tab_date_ranges['cProfile'] }}" />
                                                    <input type="hidden" name="active_tab" value="cProfile">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <button type="submit" class="btn ol-btn-primary w-100" id="submit-button"
                                                    onclick="update_date_range();"> {{ get_phrase('Filter') }}</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            @if (count($affiliate_payout_complete) > 0)
                                <div
                                    class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                                    <p class="admin-tInfo">
                                        {{ get_phrase('Showing') . ' ' . count($affiliate_payout_complete) . ' ' . get_phrase('of') . ' ' . $affiliate_payout_complete->total() . ' ' . get_phrase('data') }}
                                    </p>
                                    {{ $affiliate_payout_complete->appends(request()->query())->links() }}
                                </div>
                                <div class="table-responsive purchase_list" id="purchase_list">
                                    <table class="table eTable eTable-2 print-table">
                                        <thead>
                                            <tr>
                                                <th scope="col">#</th>
                                                <th scope="col">{{ get_phrase('Name') }}</th>
                                                <th scope="col">{{ get_phrase('Payout amount') }}</th>
                                                <th scope="col">{{ get_phrase('Request date') }}</th>
                                                <th scope="col">{{ get_phrase('Processed date') }}</th>
                                                <th scope="col">{{ get_phrase('Note') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($affiliate_payout_complete as $key => $affiliate_payouts)
                                                <tr>
                                                    <th scope="row">
                                                        <p class="row-number">{{ ++$key }}</p>
                                                    </th>
                                                    <td>
                                                        <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                            <div class="dAdmin_profile_img">
                                                                <img class="img-fluid rounded-circle image-45" width="45"
                                                                    height="45"
                                                                    src="{{ get_image($affiliate_payouts->user->photo) }}" />
                                                            </div>
                                                            <div class="ms-1">
                                                                <h4 class="title fs-14px">
                                                                    {{$affiliate_payouts->user->name }}</h4>
                                                                <p class="sub-title2 text-12px">
                                                                    {{ $affiliate_payouts->user->email }}</p>
                                                            </div>
                                                        </div>
                                                    </td>

                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            <p>{{ currency($affiliate_payouts->amount) }}</p>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            <p>
                                                                @if(is_string($affiliate_payouts->request_date))
                                                                    {{ $affiliate_payouts->request_date }}
                                                                @elseif($affiliate_payouts->request_date instanceof \DateTime || $affiliate_payouts->request_date instanceof \Carbon\Carbon)
                                                                    {{ $affiliate_payouts->request_date->format('d/m/Y') }}
                                                                @else
                                                                    N/A
                                                                @endif
                                                            </p>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            <p>
                                                                @if(is_string($affiliate_payouts->processed_date))
                                                                    {{ $affiliate_payouts->processed_date }}
                                                                @elseif($affiliate_payouts->processed_date instanceof \DateTime || $affiliate_payouts->processed_date instanceof \Carbon\Carbon)
                                                                    {{ $affiliate_payouts->processed_date->format('d/m/Y H:i') }}
                                                                @else
                                                                    N/A
                                                                @endif
                                                            </p>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            @if(!empty($affiliate_payouts->note))
                                                                <small>{!! nl2br(e($affiliate_payouts->note)) !!}</small>
                                                            @else
                                                                <small class="text-muted">{{ get_phrase('No note') }}</small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr>
                                                <th></th>
                                                <th></th>
                                                <th>{{ get_phrase('Total') }} :
                                                    {{ currency($affiliate_payout_complete_total_amount) }}
                                                </th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                @include('admin.no_data')
                            @endif
                        </div>
                        <div class="tab-pane fade @if($active_tab == 'cCanceled') show active @endif"
                            id="cCanceled" role="tabpanel" aria-labelledby="cCanceled-tab">

                            <div class="row print-d-none mt-4 row-gap-3">
                                <div class="col-md-6 pt-2 pt-md-0">
                                    @if (count($affiliate_payout_complete) > 0)
                                        <div class="custom-dropdown">
                                            <button class="dropdown-header btn ol-btn-light">
                                                {{ get_phrase('Export') }}
                                                <i class="fi-rr-file-export ms-2"></i>
                                            </button>
                                            <ul class="dropdown-list">
                                                <li>
                                                    <a class="dropdown-item" href="#"
                                                        onclick="downloadPDF('.print-table', 'accepted-payout')"><i
                                                            class="fi-rr-file-pdf"></i> {{ get_phrase('PDF') }}</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="window.print();"><i
                                                            class="fi-rr-print"></i> {{ get_phrase('Print') }}</a>
                                                </li>
                                            </ul>
                                        </div>
                                    @endif
                                </div>

                                <div class="col-md-6">
                                    <form class="form-inline" action="{{ route('admin.affiliate.withdraws') }}"
                                        method="get">
                                        <div class="row mb-4">
                                            <div class="col-md-9">
                                                <div class="mb-3 position-relative position-relative">
                                                    <input type="text"
                                                        class="form-control ol-form-control daterangepicker w-100"
                                                        name="eDateRangecCanceled"
                                                        value="{{ $tab_date_ranges['cCanceled'] }}" />
                                                    <input type="hidden" name="active_tab" value="cCanceled">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <button type="submit" class="btn ol-btn-primary w-100" id="submit-button"
                                                    onclick="update_date_range();"> {{ get_phrase('Filter') }}</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            @if (count($affiliate_payout_canceled) > 0)
                                <div
                                    class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                                    <p class="admin-tInfo">
                                        {{ get_phrase('Showing') . ' ' . count($affiliate_payout_canceled) . ' ' . get_phrase('of') . ' ' . $affiliate_payout_canceled->total() . ' ' . get_phrase('data') }}
                                    </p>
                                    {{ $affiliate_payout_canceled->appends(request()->query())->links() }}
                                </div>
                                <div class="table-responsive purchase_list" id="purchase_list">
                                    <table class="table eTable eTable-2 print-table">
                                        <thead>
                                            <tr>
                                                <th scope="col">#</th>
                                                <th scope="col">{{ get_phrase('Name') }}</th>
                                                <th scope="col">{{ get_phrase('Payout amount') }}</th>
                                                <th scope="col">{{ get_phrase('Request date') }}</th>
                                                <th scope="col">{{ get_phrase('Canceled date') }}</th>
                                                <th scope="col">{{ get_phrase('Note') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($affiliate_payout_canceled as $key => $affiliate_payouts)
                                                <tr>
                                                    <th scope="row">
                                                        <p class="row-number">{{ ++$key }}</p>
                                                    </th>
                                                    <td>
                                                        <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                            <div class="dAdmin_profile_img">
                                                                <img class="img-fluid rounded-circle image-45" width="45"
                                                                    height="45"
                                                                    src="{{ get_image($affiliate_payouts->user->photo) }}" />
                                                            </div>
                                                            <div class="ms-1">
                                                                <h4 class="title fs-14px">{{ @$affiliate_payouts->user->name }}</h4>
                                                                <p class="sub-title2 text-12px">
                                                                    {{ @$affiliate_payouts->user->email }}</p>
                                                            </div>
                                                        </div>
                                                    </td>

                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            <p>{{ currency($affiliate_payouts->amount) }}</p>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            <p>
                                                                @if(is_string($affiliate_payouts->request_date))
                                                                    {{ $affiliate_payouts->request_date }}
                                                                @elseif($affiliate_payouts->request_date instanceof \DateTime || $affiliate_payouts->request_date instanceof \Carbon\Carbon)
                                                                    {{ $affiliate_payouts->request_date->format('d/m/Y') }}
                                                                @else
                                                                    N/A
                                                                @endif
                                                            </p>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            <p>
                                                                @if(is_string($affiliate_payouts->processed_date))
                                                                    {{ $affiliate_payouts->processed_date }}
                                                                @elseif($affiliate_payouts->processed_date instanceof \DateTime || $affiliate_payouts->processed_date instanceof \Carbon\Carbon)
                                                                    {{ $affiliate_payouts->processed_date->format('d/m/Y H:i') }}
                                                                @else
                                                                    N/A
                                                                @endif
                                                            </p>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name">
                                                            @if(!empty($affiliate_payouts->note))
                                                                <small>{!! nl2br(e($affiliate_payouts->note)) !!}</small>
                                                            @else
                                                                <small class="text-muted">{{ get_phrase('No note') }}</small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr>
                                                <th></th>
                                                <th></th>
                                                <th>{{ get_phrase('Total') }} :
                                                    {{ currency($affiliate_payout_canceled_total_amount) }}
                                                </th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                @include('admin.no_data')
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script type="text/javascript">
        "use strict";

        function update_date_range() {
            var x = $("#selectedValue").html();
            $("#date_range").val(x);
        }

        // Đảm bảo các tab hiển thị đúng khi sử dụng thẻ a thay vì button
        document.addEventListener('DOMContentLoaded', function() {
            // Lấy tab hiện tại
            var activeTab = '{{ $active_tab }}';
            
            // Hiển thị nội dung tab tương ứng
            var tabContent = document.getElementById(activeTab);
            if (tabContent) {
                // Ẩn tất cả tab content
                var allTabContents = document.querySelectorAll('.tab-pane');
                allTabContents.forEach(function(tab) {
                    tab.classList.remove('show', 'active');
                });
                
                // Hiển thị tab hiện tại
                tabContent.classList.add('show', 'active');
            }
        });
    </script>
@endpush
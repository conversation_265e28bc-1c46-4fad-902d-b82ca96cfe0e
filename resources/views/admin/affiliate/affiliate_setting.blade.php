@extends('layouts.admin')
@push('title', get_phrase('Affiliate Setting'))
@push('meta')@endpush
@push('css')@endpush
@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-4 px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    {{ get_phrase('Affiliate Settings') }}
                </h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="ol-card p-4">
                @if(session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif
                
                <div class="ol-card-body">
                    <form action="{{ route('admin.affiliate.setting.store') }}" method="post" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <h3 class="title text-14px mb-3">{{ get_phrase('General Affiliate Settings') }}</h3>
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label">{{ get_phrase('Allow public Affiliate') }}</label>
                                    <select class="form-control ol-form-control ol-select2" data-toggle="select2" name="allow_affiliate" required>
                                        <option value="1" @if(isset($allow_affiliate) && $allow_affiliate->description == '1') selected @endif>
                                            {{ get_phrase('Yes') }}</option>
                                        <option value="0" @if(isset($allow_affiliate) && $allow_affiliate->description == '0') selected @endif>
                                            {{ get_phrase('No') }}</option>
                                    </select>
                                </div>
                                
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label">{{ get_phrase('Show Affiliate Notification') }}</label>
                                    <select class="form-control ol-form-control ol-select2" data-toggle="select2" name="affiliate_notification" required>
                                        <option value="1" @if(isset($affiliate_notification) && $affiliate_notification->description == '1') selected @endif>
                                            {{ get_phrase('Yes') }}</option>
                                        <option value="0" @if(isset($affiliate_notification) && $affiliate_notification->description == '0') selected @endif>
                                            {{ get_phrase('No') }}</option>
                                    </select>
                                </div>

                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="affiliate_application_note">{{ get_phrase('Affiliate application note') }}</label>
                                    <textarea class="form-control ol-form-control" name="affiliate_application_note" rows="5" cols="80">{{ isset($application_note) ? $application_note->description : '' }}</textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h3 class="title text-14px mb-3">{{ get_phrase('Revenue Settings') }}</h3>
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="affiliate_revenue">{{ get_phrase('Affiliate revenue percentage') }}</label>
                                    <div class="input-group">
                                        <input type="number" name="affiliate_revenue" id="affiliate_revenue" class="form-control ol-form-control"
                                            onkeyup="calculateAdminRevenue(this.value)" min="0" max="100" value="{{ isset($affiliate_revenue) ? $affiliate_revenue->description : '0' }}">
                                        <div class="input-group-append">
                                            <span class="input-group-text ol-form-control">%</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="admin_revenue">{{ get_phrase('Admin revenue percentage') }}</label>
                                    <div class="input-group">
                                        <input type="number" name="admin_revenue" id="admin_revenue" class="form-control ol-form-control" value="0" disabled>
                                        <div class="input-group-append">
                                            <span class="input-group-text ol-form-control">%</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- Cấu hình số tiền rút tối thiểu  -->
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="affiliate_payout_min_amount">{{ get_phrase('Minimum withdrawal amount') }} </label>
                                    <div class="input-group">
                                        <input type="number" name="affiliate_payout_min_amount" id="affiliate_payout_min_amount" class="form-control ol-form-control" min="10000" value="{{ isset($affiliate_payout_min_amount) ? $affiliate_payout_min_amount->description : '10000' }}">
                                        <div class="input-group-append">
                                            <span class="input-group-text ol-form-control">{{ get_phrase('VND') }}</span>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">{{ get_phrase('Minimum amount to withdraw') }}</small>
                                </div>
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="affiliate_payout_days">{{ get_phrase('Days before earnings become available') }}</label>
                                    <div class="input-group">
                                        <input type="number" name="affiliate_payout_days" id="affiliate_payout_days" class="form-control ol-form-control" min="0" value="{{ isset($affiliate_payout_days) ? $affiliate_payout_days->description : '0' }}">
                                        <div class="input-group-append">
                                            <span class="input-group-text ol-form-control">{{ get_phrase('days') }}</span>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">{{ get_phrase('Number of days to wait before adding affiliate earnings to the user\'s available balance') }}</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn ol-btn-primary">{{ get_phrase('Update Settings') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script type="text/javascript">
        "use strict";

        $(document).ready(function() {
            var affiliate_revenue = $('#affiliate_revenue').val();
            calculateAdminRevenue(affiliate_revenue);
        });

        function calculateAdminRevenue(affiliate_revenue) {
            if (affiliate_revenue <= 100) {
                var admin_revenue = 100 - affiliate_revenue;
                $('#admin_revenue').val(admin_revenue);
            } else {
                $('#admin_revenue').val(0);
            }
        }
    </script>
@endpush

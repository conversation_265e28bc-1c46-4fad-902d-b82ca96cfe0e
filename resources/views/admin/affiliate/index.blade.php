@extends('layouts.admin')
@push('title', get_phrase('Affiliate Manager'))
@section('content')
    <!-- Modal for adding notes -->
    <div class="modal fade" id="affiliateNoteModal" tabindex="-1" aria-labelledby="affiliateNoteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="affiliateNoteModalLabel">{{ get_phrase('Add Note') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="affiliateNoteForm" action="" method="post">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="note" class="form-label">{{ get_phrase('Note') }}</label>
                            <textarea class="form-control" id="note" name="note" rows="4" placeholder="{{ get_phrase('Enter your note here...') }}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn ol-btn-outline-secondary" data-bs-dismiss="modal">{{ get_phrase('Cancel') }}</button>
                        <button type="submit" class="btn ol-btn-primary">{{ get_phrase('Save') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="ol-card radius-8px">
        <div class="ol-card-body py-12px px-20px my-3">
            <div class="d-flex align-items-center justify-content-between flex-md-nowrap flex-wrap gap-3">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    {{ get_phrase('Manage Afiliate') }}
                </h4>

            </div>
        </div>
    </div>


    <!-- Start Admin area -->
    <div class="row">
        <div class="col-12">
            <div class="ol-card">
                <div class="ol-card-body mb-5 p-3">
                    <div class="row mb-4 mt-3">
                        <div class="col-md-6 d-flex align-items-center gap-3">
                            <div class="custom-dropdown ms-2">
                                <button class="dropdown-header btn ol-btn-light">
                                    {{ get_phrase('Export') }}
                                    <i class="fi-rr-file-export ms-2"></i>
                                </button>
                                <ul class="dropdown-list">
                                    <li>
                                        <a class="dropdown-item export-btn" href="#"
                                            onclick="downloadPDF('.print-table', 'affiliate-list')"><i
                                                class="fi-rr-file-pdf"></i> {{ get_phrase('PDF') }}</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item export-btn" href="#" onclick="window.print();"><i
                                                class="fi-rr-print"></i> {{ get_phrase('Print') }}</a>
                                    </li>
                                </ul>
                            </div>

                        </div>
                       
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            @if ($affiliates->count() > 0)
                                <div
                                    class="admin-tInfo-pagi d-flex justify-content-md-between justify-content-center align-items-center gr-15 flex-wrap">
                                    <p class="admin-tInfo">
                                        {{ get_phrase('Showing') . ' ' . count($affiliates) . ' ' . get_phrase('of') . ' ' . $affiliates->total() . ' ' . get_phrase('data') }}
                                    </p>
                                </div>
                                <div class="table-responsive course_list overflow-auto overflow-auto" id="course_list">
                                    <table class="eTable eTable-2 print-table table">
                                        <thead>
                                            <tr>
                                                <th scope="col">#</th>
                                                <th scope="col">{{ get_phrase('Date') }}</th>
                                                <th scope="col">{{ get_phrase('Affiliator') }}</th>
                                                <th scope="col">{{ get_phrase('Course') }}</th>
                                                <th scope="col">{{ get_phrase('Affiliated amount') }}</th>
                                                <th scope="col" class="print-d-none">{{ get_phrase('Status') }}</th>
                                                <th scope="col">{{ get_phrase('Buyer') }}</th>
                                                <th scope="col">{{ get_phrase('Note') }}</th>
                                                <th scope="col" class="print-d-none">{{ get_phrase('Action') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($affiliates as $key => $affiliate)
                                                <tr>
                                                    <th scope="row">
                                                        <p class="row-number">{{ ++$key }}</p>
                                                    </th>
                                                    <td>
                                                        <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                            <div class="dAdmin_profile_name">
                                                                <h4 class="title fs-14px">
                                                                    {{ $affiliate->created_at->format('d-m-Y') }}
                                                                </h4>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="sub-title2 text-12px">
                                                            {{ @$affiliate->user->name }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="sub-title2 text-12px">
                                                            {{ $affiliate->course->title }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="sub-title2 text-12px">
                                                            {{ currency($affiliate->amount) }}
                                                        </div>
                                                    </td>
                                                    <td class="print-d-none">
                                                        @if($affiliate->status == 0)
                                                            <span class="badge bg-danger">{{ get_phrase('Canceled') }}</span>
                                                        @elseif($affiliate->status == 1)
                                                            <span class="badge bg-success">{{ get_phrase('Completed') }}</span>
                                                        @elseif($affiliate->status == 2)
                                                            <span class="badge bg-warning">{{ get_phrase('Waiting') }}</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name min-w-150px">
                                                            {{ @$affiliate->customer->name }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="dAdmin_info_name min-w-150px">
                                                            @if (!empty($affiliate->note))
                                                                <p>{{ $affiliate->note }}</p>
                                                            @else
                                                                <p class="text-muted">{{ get_phrase('No note') }}</p>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td class="print-d-none">
                                                    @if($affiliate->status == 2)
                                                    <div class="dropdown ol-icon-dropdown ol-icon-dropdown-transparent">
                                                        <button class="btn ol-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <span class="fi-rr-menu-dots-vertical"></span>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                                    <li>
                                                                        <button type="button" 
                                                                            class="dropdown-item open-affiliate-modal" 
                                                                            data-affiliate-id="{{ $affiliate->id }}" 
                                                                            data-status="1"
                                                                            data-action="{{ route('admin.affiliate.status', ['id' => $affiliate->id, 'status' => 1]) }}">
                                                                            <i class="fi-rr-check text-success me-1"></i>
                                                                            {{ get_phrase('Completed') }}
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button type="button" 
                                                                            class="dropdown-item open-affiliate-modal" 
                                                                            data-affiliate-id="{{ $affiliate->id }}" 
                                                                            data-status="0"
                                                                            data-action="{{ route('admin.affiliate.status', ['id' => $affiliate->id, 'status' => 0]) }}">
                                                                            <i class="fi-rr-ban text-danger me-1"></i>
                                                                            {{ get_phrase('Canceled') }}
                                                                        </button>
                                                                    </li>
                                                                
                                                        </ul>
                                                    </div>


                                                    @endif

                                                        
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <div
                                    class="admin-tInfo-pagi d-flex justify-content-md-between justify-content-center align-items-center gr-15 flex-wrap">
                                    <p class="admin-tInfo">
                                        {{ get_phrase('Showing') . ' ' . count($affiliates) . ' ' . get_phrase('of') . ' ' . $affiliates->total() . ' ' . get_phrase('data') }}
                                    </p>
                                    {{ $affiliates->links() }}
                                </div>
                            @else
                                @include('admin.no_data')
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Admin area -->
@endsection

@push('js')
    <script type="text/javascript">
        "use strict";

        // Xử lý khi người dùng click vào nút Completed hoặc Cancel
        $(document).ready(function() {
            $('.open-affiliate-modal').on('click', function() {
                // Lấy thông tin từ data attributes
                var affiliateId = $(this).data('affiliate-id');
                var status = $(this).data('status');
                var action = $(this).data('action');
                
                // Thiết lập action cho form
                $('#affiliateNoteForm').attr('action', action);
                
                // Hiện title của modal
                if (status == 1) {
                    $('#affiliateNoteModalLabel').text(
                        "{{ get_phrase('Complete Affiliate - Add Note') }}"
                    );
                } else {
                    $('#affiliateNoteModalLabel').text(
                        "{{ get_phrase('Cancel Affiliate - Add Note') }}"
                    );
                }
                
                // Làm trống textarea
                $('#note').val('');
                
                // Hiển thị modal
                $('#affiliateNoteModal').modal('show');
            });
        });
    </script>
@endpush
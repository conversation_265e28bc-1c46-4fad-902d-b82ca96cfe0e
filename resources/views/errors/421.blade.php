@extends('layouts.default')
@push('title', '<PERSON><PERSON>ớ<PERSON> hạn thiết bị đăng nhập')
@push('meta')@endpush
@push('css')
    <style>
        .error-container {
            padding: 60px 0;
        }

        .error-image {
            max-width: 100%;
            height: auto;
        }

        .error-title {
            font-size: 32px;
            font-weight: 700;
            color: #e63946;
            margin-bottom: 20px;
        }

        .error-message {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .contact-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .home-button {
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .action-item {
            margin-bottom: 12px;
            font-weight: 500;
        }

        .device-limit {
            font-weight: bold;
            color: #e63946;
        }
    </style>
@endpush
@section('content')
    <section class="error-container">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-5 col-md-6 mb-4 mb-md-0">
                    <img src="{{asset('assets/frontend/default/image/login.gif')}}" alt="Giới hạn thiết bị đăng nhập"
                         class="error-image">
                </div>
                <div class="col-lg-6 col-md-6 ms-auto">
                    <h1 class="error-title">Giới hạn thiết bị đăng nhập</h1>

                    <div class="error-message">
                        <p>Xin chào! Tài khoản của bạn đã vượt quá số lượng thiết bị được phép đăng nhập cùng lúc.</p>

                        <div class="warning-box">
                            <p class="mb-2"><strong>Thông tin chi tiết:</strong></p>
                            <ul class="mb-0 ps-3">
                                <li class="action-item">Bạn chỉ được đăng nhập trên tối đa <span class="device-limit">{{ get_settings('device_limitation') }}</span> thiết bị.</li>
                                <li class="action-item">Hãy đăng xuất khỏi một số thiết bị khác để có thể đăng nhập mới.</li>
                            </ul>

                            <p class="mt-3 mb-0 text-muted fst-italic">Nếu bạn cần đăng nhập trên nhiều thiết bị hơn, vui lòng liên hệ với quản trị viên để được hỗ trợ.</p>
                        </div>
                    </div>

                    <div class="contact-info">
                        <p class="mb-3">Nếu bạn cần hỗ trợ thêm, hãy liên hệ với chúng tôi:</p>
                        <p class="mb-2">
                            <i class="fas fa-envelope me-2 text-primary"></i> Email:
                            @php
                                $contact_info = json_decode(get_frontend_settings('contact_info'), true);
                                if (is_array($contact_info) && array_key_exists('email', $contact_info)) {
                                    echo $contact_info['email'];
                                }
                            @endphp
                        </p>
                        @php
                            if (is_array($contact_info) && array_key_exists('phone', $contact_info)) {
                                echo '<p class="mb-0"><i class="fas fa-phone-alt me-2 text-success"></i> Điện thoại: '.$contact_info['phone'].'</p>';
                            }
                        @endphp
                    </div>

                    <div class="d-flex gap-3">
                        <a class="eBtn gradient home-button" href="{{route('home')}}">
                            <i class="fas fa-home me-2"></i>Quay lại trang chủ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script>
        // Thêm chức năng JavaScript nếu cần
    </script>
@endpush

@extends('layouts.default')
@push('title', '<PERSON><PERSON><PERSON> b<PERSON>o b<PERSON>o mật tài kho<PERSON>n')
@push('meta')@endpush
@push('css')
    <style>
        .error-container {
            padding: 60px 0;
        }

        .error-image {
            max-width: 100%;
            height: auto;
        }

        .error-title {
            font-size: 32px;
            font-weight: 700;
            color: #e63946;
            margin-bottom: 20px;
        }

        .error-message {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .contact-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .home-button {
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .action-item {
            margin-bottom: 12px;
            font-weight: 500;
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 5px;
        }
    </style>
@endpush
@section('content')
    <section class="error-container">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-5 col-md-6 mb-4 mb-md-0">
                    <img src="{{asset('assets/frontend/default/image/451.jpg')}}" alt="Cảnh báo sử dụng tài khoản"
                         class="error-image">
                </div>
                <div class="col-lg-6 col-md-6 ms-auto">
                    <h1 class="error-title">Bảo mật tài khoản của bạn</h1>

                    <div class="error-message">
                        <p>Xin chào! Chúng tôi phát hiện tài khoản của bạn có thể đang được sử dụng bởi nhiều người khác nhau.</p>
                        
                        <div class="warning-box">

                            <p class="mb-2"><strong>Bạn cần làm gì?</strong></p>
                            <ul class="mb-0 ps-3">
                            <li class="action-item">Nếu bạn không chia sẻ tài khoản: Hãy đổi mật khẩu ngay để đảm bảo an toàn cho tài khoản của bạn.</li>
                            </ul>
                            
                            <p class="mt-3 mb-0 text-muted fst-italic">Nếu tình trạng này tiếp tục, chúng tôi có thể phải tạm khóa tài khoản để bảo vệ dữ liệu học tập của bạn.</p>
                        </div>
                    </div>

                    <div class="contact-info">
                        <p class="mb-3">Nếu bạn cần hỗ trợ thêm hoặc cho rằng đây là sự nhầm lẫn, hãy liên hệ với chúng tôi:</p>
                        <p class="mb-2">
                            <i class="fas fa-envelope me-2 text-primary"></i> Email:
                            @php
                                $contact_info = json_decode(get_frontend_settings('contact_info'), true);
                                if (is_array($contact_info) && array_key_exists('email', $contact_info)) {
                                    echo $contact_info['email'];
                                }
                            @endphp
                        </p>
                        @php
                            if (is_array($contact_info) && array_key_exists('phone', $contact_info)) {
                                echo '<p class="mb-0"><i class="fas fa-phone-alt me-2 text-success"></i> Điện thoại: '.$contact_info['phone'].'</p>';
                            }
                        @endphp
                    </div>

                    <div class="d-flex gap-3">
                        <a class="eBtn gradient home-button" href="{{route('home')}}">
                            <i class="fas fa-home me-2"></i>Tiếp tục
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script>
        // Thêm chức năng JavaScript nếu cần
    </script>
@endpush

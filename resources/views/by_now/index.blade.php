<script>
    $(document).ready(function () {
        // Constants
        const ROUTES = {
            register: '{{ route('register') }}',
            login: '{{ route('login') }}',
            checkCoupon: '{{ route('checkcoupon', $course->id) }}',
            check_payment_success_order: '{{ route('check.payment.success.order') }}',
            get_coupons: '{{ route('get.coupons.by.course', $course->id) }}'
        };

        const CSRF_TOKEN = '{{ csrf_token() }}';

        // DOM Elements Cache
        const $email = $('#email');
        const $password = $('#password');
        const $phone = $('#phone');
        const $orderId = $('#order_id');
        const $courseId = $('#course_id');
        const $price = $('#price');
        const $checkbox = $('#checkbox');
        const $couponList = $('#coupon_list');
        const $successPopup = $('#success_popup');
        const $qrPopup = $('#qr_popup');
        const $registerButton = $('#register_button');
        const $byNowButton = $('#by_now_button');
        const $cancelCouponButton = $('#cancel_coupon_button');
        const $appliedCouponInfo = $('#applied_coupon_info');
        
        // Biến lưu trữ giá ban đầu của khóa học
        let originalPrice = {{ $course->price ?? 0 }};

        /**
         * Utility Functions
         */

        /**
         * Hiển thị thông báo
         * @param {string} message - Nội dung thông báo
         * @param {boolean} isError - Có phải là thông báo lỗi hay không
         */
        function showMessage(message, isError = true) {
            alert(message);
        }

        /**
         * API Functions
         */

        /**
         * Thực hiện đăng ký tài khoản
         * @returns {Promise} - Promise từ request
         */
        function registerUser() {
            return $.post(ROUTES.register, {
                _token: CSRF_TOKEN,
                email: $email.val(),
                password: $password.val(),
                phone: $phone.val()
            });
        }

        /**
         * Thực hiện đăng nhập
         * @returns {Promise} - Promise từ request
         */
        function loginUser() {
            return $.post(ROUTES.login, {
                _token: CSRF_TOKEN,
                email: $email.val(),
                password: $password.val()
            });
        }

        /**
         * Kiểm tra mã giảm giá
         * @param {string} couponCode - Mã giảm giá cần kiểm tra
         * @returns {Promise} - Promise từ request
         */
        function checkCoupon(couponCode) {
            return $.post(ROUTES.checkCoupon, {
                _token: CSRF_TOKEN,
                coupon_code: couponCode
            });
        }

        /**
         * Lấy danh sách mã giảm giá
         * @returns {Promise} - Promise từ request
         */
        function getCoupons() {
            return $.get(ROUTES.get_coupons);
        }

        /**
         * Kiểm tra trạng thái thanh toán
         * @param {string} orderId - ID của đơn hàng
         * @param {string} courseId - ID của khóa học
         * @returns {Promise} - Promise từ request
         */
        function checkPaymentStatus(orderId, courseId) {
            return $.post(ROUTES.check_payment_success_order, {
                _token: CSRF_TOKEN,
                order_id: orderId,
                course_id: courseId
            });
        }

        /**
         * Handler Functions
         */

        /**
         * Xử lý đăng ký tài khoản
         * @param {Event} e - Sự kiện
         */
        function handleRegister(e) {
            registerUser()
                .done(function (response) {
                    if (!response.error) {
                        location.reload();
                    } else {
                        showMessage('Failed to register');
                    }
                })
                .fail(function () {
                    showMessage('Server error occurred during registration');
                });
        }

        /**
         * Xử lý đăng nhập
         * @param {Event} e - Sự kiện
         */
        function handleLogin(e) {
            loginUser()
                .done(function (response) {
                    if (!response.error) {
                        location.reload();
                    } else {
                        showMessage('Failed to login');
                    }
                })
                .fail(function () {
                    showMessage('Server error occurred during login');
                });
        }

        /**
         * Xử lý kiểm tra mã giảm giá
         * @param {Event} e - Sự kiện
         */
        function handleCheckCoupon(e) {
            const couponCode = $(this).data('coupon-code');

            checkCoupon(couponCode)
                .done(function (response) {
                    if (!response.error) {
                        showMessage('Coupon applied successfully', false);
                        
                        // Cập nhật giá sau khi áp dụng mã giảm giá
                        const finalPrice = response.data.course.price - response.data.coupon.discount;
                        $price.val(finalPrice);
                        
                        // Lưu giá ban đầu để dùng khi hủy mã giảm giá
                        originalPrice = response.data.course.price;
                        
                        // Hiển thị thông tin mã giảm giá đã áp dụng
                        $appliedCouponInfo.html(
                            `<div class="alert alert-success">
                                <strong>Mã giảm giá:</strong> ${response.data.coupon.code} 
                                <span class="ms-2">(Giảm: ${response.data.coupon.discount})</span>
                            </div>`
                        ).show();
                        
                        // Ẩn nút áp dụng coupon
                        $byNowButton.hide();
                        
                        // Hiển thị nút hủy coupon
                        $cancelCouponButton.show();
                    } else {
                        showMessage('Failed to add course to your cart');
                    }
                })
                .fail(function () {
                    showMessage('Server error occurred while checking coupon');
                });
        }

        /**
         * Xử lý hủy mã giảm giá
         * @param {Event} e - Sự kiện
         */
        function handleCancelCoupon(e) {
            e.preventDefault();
            
            // Khôi phục giá ban đầu
            $price.val(originalPrice);
            
            // Xóa thông tin mã giảm giá đã áp dụng
            $appliedCouponInfo.empty().hide();
            
            // Hiển thị lại nút áp dụng coupon
            $byNowButton.show();
            
            // Ẩn nút hủy coupon
            $cancelCouponButton.hide();
            
            showMessage('Mã giảm giá đã được hủy', false);
        }

        /**
         * Xử lý hiển thị danh sách mã giảm giá
         * @param {Event} e - Sự kiện
         */
        function handleShowCoupons(e) {
            getCoupons()
                .done(function (response) {
                    if (!response.error) {
                        // Tạo HTML cho danh sách mã giảm giá
                        const couponItems = response.data.coupons.map(function (coupon) {
                            return `<li class="coupon-item">
                                <span class="coupon-code">${coupon.code}</span>
                                <span class="coupon-discount">${coupon.discount}</span>
                            </li>`;
                        }).join('');
                        
                        $couponList.html(couponItems);
                    } else {
                        showMessage('Failed to get coupon list');
                    }
                })
                .fail(function () {
                    showMessage('Server error occurred while fetching coupons');
                });
        }

        /**
         * Xử lý đăng ký form
         * @param {Event} e - Sự kiện
         */
        function handleRegisterForm(e) {
            e.preventDefault();

            // Kiểm tra điều khoản đã được chấp nhận
            if (!$checkbox.is(':checked')) {
                showMessage('Please check the terms and conditions');
                return;
            }

            registerUser()
                .done(function (response) {
                    if (!response.error) {
                        $registerButton.text(response.data.user.name);
                        $price.val(response.data.user.balance);
                    } else {
                        showMessage('Failed to register');
                    }
                })
                .fail(function () {
                    showMessage('Server error occurred during registration');
                });
        }

        /**
         * Xử lý thanh toán
         * @param {Event} e - Sự kiện
         */
        function handlePayment(e) {
            const checkInterval = 5000; // 5 giây

            // Kiểm tra trạng thái thanh toán mỗi 5 giây
            const paymentChecker = setInterval(function () {
                checkPaymentStatus($orderId.val(), $courseId.val())
                    .done(function (response) {
                        if (!response.error) {
                            // Hiển thị thông báo thành công
                            $successPopup.show();
                            $qrPopup.hide();
                            
                            // Dừng việc kiểm tra
                            clearInterval(paymentChecker);
                        }
                    })
                    .fail(function () {
                        showMessage('Server error occurred while checking payment status');
                        clearInterval(paymentChecker);
                    });
            }, checkInterval);
        }

        // Event bindings
        $registerButton.click(handleRegister);
        $('#login_button').click(handleLogin);
        $byNowButton.click(handleCheckCoupon);
        $('#register_form').submit(handleRegisterForm);
        $('#pay_button').click(handlePayment);
        $('#coupon_button').click(handleShowCoupons);
        $cancelCouponButton.click(handleCancelCoupon);
    });
</script>
@extends('layouts.default')
@push('title', get_phrase('Blog Details'))
@push('meta')@endpush
@push('css')
    <style>
        .playing-breadcum {
            height: 350px;
        }

        .breadcum-area {
            z-index: -1;
        }
    </style>
@endpush
@section('content')
    @php
        $total_comments = count_comments_by_blog_id($blog_details->id);
        $total_likes = count_likes_by_blog_id($blog_details->id);
    @endphp
    <!------------------- Breadcum Area Start  ------>
    <section class="breadcum-area playing-breadcum details-breadcum">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="eNtry-breadcum">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('home') }}">{{ get_phrase('Home') }}</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('blogs') }}">{{ get_phrase('Blogs') }}</a></li>
                                <li class="breadcrumb-item active" aria-current="page">{{ get_phrase('Blog Details') }}</li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!------------------- Breadcum Area End  --------->

    <!------------------- Blog Details Area Start  --------->
    <section class="blog-details">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ps-box blog-box">

                        <div class="details-intro">
                            <h4 class="g-title text-center f-40 mt-4">{{ $blog_details->title }}</h4>
                            <div class="content">
                                {!! $blog_details->description !!}
                            </div>

                            <ul class="course-motion-top flex-wrap gap-4 justify-content-center">
                                <li>
                                    <div class="figar d-flex align-items-center mb-0 me-auto">
                                        <img src="{{ get_image($blog_details->author_photo) }}" alt="author-image">
                                        <p class="description">{{ $blog_details->author_name }}</p>
                                    </div>
                                </li>
                                @if ($blog_details->keywords)
                                    <li>
                                        <img class="pro-20" src="{{ asset('assets/frontend/default/image/elearn.png') }}" alt="blog-tag">
                                        @php
                                            $tags = json_decode($blog_details->keywords, true);
                                            if (is_array($tags) && count($tags) > 0) {
                                                $tags = array_column($tags, 'value');
                                            }
                                        @endphp
                                        {{ $tags ? implode(', ', $tags) : '' }}
                                    </li>
                                @endif
                                <li>
                                    <img class="pro-20" src="{{ asset('assets/frontend/default/image/elearn2.png') }}" alt="blog-comment">
                                    {{ $total_comments }}
                                </li>
                                <li>
                                    <img class="pro-20" src="{{ asset('assets/frontend/default/image/elearn3.png') }}" alt="created-date">
                                    {{ date('d M, Y', strtotime($blog_details->created_at)) }}
                                </li>
                            </ul>
                        </div>





                    </div>
                </div>
            </div>
        </div>
    </section>
    <!------------------- Blog Details Area End  --------->
@endsection
@push('js')
    <script>
        "use strict";
        $(document).ready(function() {
            $('.replay').on('click', function(e) {
                e.preventDefault();
                let comment_id = $(this).attr('id');

                if ($('#replay-' + comment_id).hasClass('d-none')) {
                    $('#replay-' + comment_id).removeClass('d-none');
                } else {
                    $('#replay-' + comment_id).addClass('d-none');
                }
                $('.replay-form:not(#replay-' + comment_id + ')').addClass('d-none');
            });

            $('.like-svg').on('click', function(e) {
                e.preventDefault();
                $.ajax({
                    type: "get",
                    url: "{{ route('blog.like') }}",
                    data: {
                        blog_id: "{{ $blog_details->id }}",
                    },
                    success: function(response) {
                        let likes = +($('#total-blog-likes').text());
                        if (response.like) {
                            $('.like-svg').addClass('active');
                        } else {
                            $('.like-svg').removeClass('active');
                        }
                    }
                });
            });
        });
    </script>
@endpush

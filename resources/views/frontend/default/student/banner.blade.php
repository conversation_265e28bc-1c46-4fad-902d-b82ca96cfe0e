    <!------------ banner area start ------------>
    <section class="Mycourses-wrapper">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="my-panel">
                        <div class="over-image">
                            <img src="{{ asset('assets/frontend/default/image/courses-bg.png') }}" class="banner"
                                alt="student-panel-banner">

                            <button class="upload-image-btn"
                                onclick="ajaxModal('{{ route('modal', ['frontend.default.upload_profile_pic', 'id' => auth()->user()->id]) }}', '{{ get_phrase('Upload picture') }}')">
                                <img src="{{ get_image(auth()->user()->photo) }}" alt="user-image">

                                <p class="upload-hover-text">{{ get_phrase('Upload new') }}</p>
                            </button>
                        </div>
                        <div class="row align-items-center mt-4">
                            <div class="col-lg-8 col-md-7">
                                <div class="my-info">
                                    <h4 class="g-title">{{ auth()->user()->name }}</h4>
                                    <ul class="d-flex align-items-center gap-4 mt-2">
                                        <li>
                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M6.71503 13.333H16.2022C16.2663 13.333 16.325 13.3063 16.3785 13.2529C16.4319 13.1994 16.4586 13.1407 16.4586 13.0766V3.58943C16.4586 3.52531 16.4319 3.46654 16.3785 3.41311C16.325 3.3597 16.2663 3.33299 16.2022 3.33299H14.7919V8.14065C14.7919 8.29129 14.7292 8.40427 14.6036 8.47959C14.4781 8.55491 14.3512 8.54931 14.223 8.46278L13.3064 7.92272C13.1782 7.84472 13.0449 7.80572 12.9065 7.80572C12.7682 7.80572 12.6419 7.84472 12.5275 7.92272L11.6109 8.46278C11.4741 8.54931 11.3451 8.55491 11.2238 8.47959C11.1026 8.40427 11.042 8.29129 11.042 8.14065V3.33299H6.71503C6.65092 3.33299 6.59216 3.3597 6.53874 3.41311C6.48531 3.46654 6.4586 3.52531 6.4586 3.58943V13.0766C6.4586 13.1407 6.48531 13.1994 6.53874 13.2529C6.59216 13.3063 6.65092 13.333 6.71503 13.333ZM6.71503 14.583C6.29409 14.583 5.93779 14.4371 5.64614 14.1455C5.35447 13.8538 5.20864 13.4975 5.20864 13.0766V3.58943C5.20864 3.16848 5.35447 2.81217 5.64614 2.52051C5.93779 2.22884 6.29409 2.08301 6.71503 2.08301H16.2022C16.6231 2.08301 16.9794 2.22884 17.2711 2.52051C17.5627 2.81217 17.7086 3.16848 17.7086 3.58943V13.0766C17.7086 13.4975 17.5627 13.8538 17.2711 14.1455C16.9794 14.4371 16.6231 14.583 16.2022 14.583H6.71503ZM3.79839 17.4996C3.37746 17.4996 3.02116 17.3538 2.72949 17.0621C2.43783 16.7704 2.29199 16.4141 2.29199 15.9932V5.88107C2.29199 5.70372 2.35182 5.55522 2.47147 5.43557C2.59114 5.31592 2.73964 5.25609 2.91697 5.25609C3.09432 5.25609 3.24283 5.31592 3.36249 5.43557C3.48214 5.55522 3.54197 5.70372 3.54197 5.88107V15.9932C3.54197 16.0573 3.56868 16.1161 3.6221 16.1695C3.67553 16.2229 3.73429 16.2496 3.79839 16.2496H13.9105C14.0879 16.2496 14.2364 16.3095 14.356 16.4291C14.4757 16.5488 14.5355 16.6973 14.5355 16.8746C14.5355 17.052 14.4757 17.2005 14.356 17.3201C14.2364 17.4398 14.0879 17.4996 13.9105 17.4996H3.79839Z"
                                                    fill="#6B7385" />
                                            </svg>
                                            {{ course_enrollments(auth()->user()->id) }}
                                            {{ get_phrase('Course Enrolled') }}
                                        </li>
                                        <li>
                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M12.618 16.2496L11.0541 17.7977C10.765 18.0851 10.4131 18.2288 9.99838 18.2288C9.58362 18.2288 9.23254 18.0851 8.94515 17.7977L7.3813 16.2496H5.25607C4.84181 16.2496 4.48717 16.1021 4.19217 15.8071C3.89716 15.5121 3.74965 15.1575 3.74965 14.7432V12.618L2.20159 11.0541C1.9142 10.765 1.77051 10.4131 1.77051 9.99838C1.77051 9.58362 1.9142 9.23254 2.20159 8.94515L3.74965 7.3813V5.25607C3.74965 4.84181 3.89716 4.48717 4.19217 4.19217C4.48717 3.89716 4.84181 3.74965 5.25607 3.74965H7.3813L8.94515 2.20159C9.23422 1.9142 9.58613 1.77051 10.0009 1.77051C10.4156 1.77051 10.7667 1.9142 11.0541 2.20159L12.618 3.74965H14.7432C15.1575 3.74965 15.5121 3.89716 15.8071 4.19217C16.1021 4.48717 16.2496 4.84181 16.2496 5.25607V7.3813L17.7977 8.94515C18.0851 9.23422 18.2288 9.58613 18.2288 10.0009C18.2288 10.4156 18.0851 10.7667 17.7977 11.0541L16.2496 12.618V14.7432C16.2496 15.1575 16.1021 15.5121 15.8071 15.8071C15.5121 16.1021 15.1575 16.2496 14.7432 16.2496H12.618ZM9.99963 17.083L12.083 14.9996H14.9996V12.083L17.083 9.99963L14.9996 7.9163V4.99963H12.083L9.99963 2.9163L7.9163 4.99963H4.99963V7.9163L2.9163 9.99963L4.99963 12.083V14.9996H7.9163L9.99963 17.083ZM9.99963 11.8858L11.4788 12.7881C11.6155 12.8746 11.7536 12.8698 11.893 12.7737C12.0325 12.6775 12.084 12.5472 12.0477 12.3826L11.6551 10.6903L12.9739 9.55155C13.1032 9.43574 13.1414 9.30038 13.0885 9.14547C13.0357 8.99055 12.9227 8.90615 12.7496 8.89226L11.0237 8.74642L10.3426 7.1583C10.2792 7.00766 10.165 6.93234 10.0002 6.93234C9.83528 6.93234 9.72079 7.00766 9.65669 7.1583L8.97559 8.74642L7.24965 8.89226C7.07657 8.90615 6.96359 8.99055 6.91072 9.14547C6.85783 9.30038 6.89604 9.43574 7.02534 9.55155L8.34419 10.6903L7.95157 12.3826C7.91525 12.5472 7.9668 12.6775 8.10622 12.7737C8.24563 12.8698 8.38372 12.8746 8.52047 12.7881L9.99963 11.8858Z"
                                                    fill="#6B7385" />
                                            </svg>
                                            {{ count_user_certificate(auth()->user()->id) }}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-5">
                                @if (auth()->user()->role == 'student')
                                    <div class="my-course-btn">
                                        <a href="{{ route('become.instructor') }}"
                                            class="eBtn gradient">{{ get_phrase('Become an instructor') }}</a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!------------ banner area end ------------>

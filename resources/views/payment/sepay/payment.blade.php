<style>
    .sepay-box {
        border: 1px solid #1977f2;
        border-radius: 15px;
    }

    .box-title {
        padding-bottom: 0.5rem;
        text-align: center;
        font-weight: 700;
        padding-top: 0.5rem;
        border-bottom: 1px solid #1977f2;
    }

    .sepay-pay-info {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 1.25rem;
        align-items: center;
    }

    #qrcode {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 150px;
        height: 150px;
        background: white;
        border: 2px solid #2b2b6b;
        padding: 10px;
    }

    #qrcode img {
        width: 100%;
        height: 100%;
    }

    .sepay-pay-info .qr-box {
        flex: none;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }

    .sepay-pay-info .qr-box .qr-title {
        text-align: center;
    }

    .sepay-pay-info .qr-box .qr-zone {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .qr-container {
        position: relative;
        width: 290px;
        margin: 10px auto;
    }

    .template-image {
        width: 100%;
        height: auto;
    }

    .sepay-pay-info .qr-element .qr-image {
        width: 100%;
    }

    .sepay-pay-info .qr-top-border {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 30%;
        display: flex;
        justify-content: space-between;
    }

    .sepay-pay-info .qr-top-border:before {
        content: "";
        width: 30%;
        height: 100%;
        border-left: 1px solid green;
        border-top: 1px solid green;
    }

    .sepay-pay-info .qr-top-border:after {
        border-right: 1px solid green;
        border-top: 1px solid green;
        content: "";
        width: 30%;
        height: 100%;
    }


    .sepay-pay-info .download-qr {
        display: flex;
        align-items: center;
        justify-items: center;
    }

    .sepay-pay-info .button-qr {
        margin-left: auto;
        margin-right: auto;
        display: inline-flex;
        padding: 0.5rem 0.75rem;
        background-color: #006dd6;
        color: #fff;
        text-decoration: none;
        gap: 0.25rem;
        align-items: center;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        line-height: 1rem;
        font-weight: 500;
    }

    .sepay-pay-info .button-qr svg {
        width: 1rem;
        height: 1rem;
        flex: none;
    }

    .sepay-pay-info .manual-box {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        gap: 1rem;
        justify-content: space-between;
    }

    .manual-title {
        text-align: center;
    }

    .bank-info {
        border: 1px solid rgb(226 232 240);
        border-radius: 0.375rem;
        overflow: hidden;
    }

    .banner {
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid rgb(226 232 240);
    }

    .bank-logo {
        max-height: 3rem;
        margin-left: auto;
        margin-right: auto;
    }


    .bank-info-table {
        display: table;
        width: 100%;
    }

    .bank-info-cell {
        display: table-cell;
        padding: 0.5rem 0.75rem;
    }

    .bank-info-cell:last-child {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
        padding-bottom: 0.5rem;
        margin-top: -0.5rem !important;
        border-bottom: 1px solid rgb(226 232 240);
    }

    .bank-info-row {
        display: flex;
        flex-direction: column;
    }

    .bank-info-row-group {
        display: table-row-group;
    }

    .bank-info-value {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }


    .note {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
        border: 1px solid #e0cd9d;
        padding: 0.5rem;
        margin: 0.5rem;
        background-color: #faebd7a1;
        border-radius: 0.375rem;
    }

    .note svg {
        color: #b09b66;
        width: 1.25rem;
        height: 1.25rem;
        margin-top: 0.125rem;
        flex: none;
    }

    .bank-info-row {
        display: table-row !important;
        flex-direction: unset;
    }

    .bank-info-cell {
        display: table-cell;
        padding: 0.5rem 0.75rem;
    }

    .bank-info-value {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .sepay-pay-footer {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        justify-content: center;
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .sepay-pay-footer img {
        width: 1.25rem;
        height: 1.25rem;
    }
</style>
@php

    $payment_gateway=$data['payment_gateway'];
    $payment_details=$data['payment_details'];
    // Start common code of all payment gateway
    $keys = json_decode($payment_gateway->keys, true);
    $personal  = $keys['name'];
    $bank_name = $keys['bank'];
    $account_number = $keys['account_number'];
    $price= $payment_details['payable_amount'];
    $description = $data['page_data']['description']

@endphp

<div class="sepay-box">
    <div class="box-title">
        Thanh toán qua chuyển khoản ngân hàng
    </div>
    <div class="sepay-message">
    </div>
    <div class="sepay-pay-info">
        <!-- QR method -->
        <div class="qr-box">
            <div class="qr-title">
                Cách 1: Mở app ngân hàng/ Ví và <b>quét mã QR</b>
            </div>
            <div class="qr-zone">
                <div class="qr-element">
                    <div class="qr-top-border"></div>

                    <div class="qr-container">
                        <img src="https://loyalty.gofood.vn/qrcode.png" class="template-image" alt="VietQR Template">
                        <div id="qrcode"></div>
                    </div>
                </div>
            </div>
            <div style="margin-top: -1rem;"></div>
        </div>
        <!-- /QR method -->

        <!-- Manual method -->
        <div class="manual-box">
            <div class="manual-title">
                Cách 2: Chuyển khoản <b>thủ công</b> theo thông tin
            </div>

            <div class="bank-info">
                <div class="bank-info-table">
                    <div class="bank-info-row-group">
                        <div class="bank-info-row">
                            <div class="bank-info-cell">Ngân hàng</div>
                            <div class="bank-info-cell">
                                <div class="bank-info-value">
                                    {{$bank_name}}
                                    <span id="sepay_copy_account_number">
                                                        <a id="sepay_copy_account_number_btn" href="javascript:;">
                                                            <svg width="15" height="15" viewBox="0 0 20 20" fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                      d="M6.625 3.125C6.34886 3.125 6.125 3.34886 6.125 3.625V4.875H13.375C14.3415 4.875 15.125 5.6585 15.125 6.625V13.875H16.375C16.6511 13.875 16.875 13.6511 16.875 13.375V3.625C16.875 3.34886 16.6511 3.125 16.375 3.125H6.625ZM15.125 15.125H16.375C17.3415 15.125 18.125 14.3415 18.125 13.375V3.625C18.125 2.6585 17.3415 1.875 16.375 1.875H6.625C5.6585 1.875 4.875 2.6585 4.875 3.625V4.875H3.625C2.6585 4.875 1.875 5.6585 1.875 6.625V16.375C1.875 17.3415 2.6585 18.125 3.625 18.125H13.375C14.3415 18.125 15.125 17.3415 15.125 16.375V15.125ZM13.875 6.625C13.875 6.34886 13.6511 6.125 13.375 6.125H3.625C3.34886 6.125 3.125 6.34886 3.125 6.625V16.375C3.125 16.6511 3.34886 16.875 3.625 16.875H13.375C13.6511 16.875 13.875 16.6511 13.875 16.375V6.625Z"
                                                                      fill="rgba(51, 102, 255, 1)"></path>
                                                            </svg>
                                                        </a>
                                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="bank-info-row">
                            <div class="bank-info-cell">Người nhận</div>
                            <div class="bank-info-cell">
                                <div class="bank-info-value">
                                    {{$personal}}
                                    <span id="sepay_copy_account_number">
                                                        <a id="sepay_copy_account_number_btn" href="javascript:;">
                                                            <svg width="15" height="15" viewBox="0 0 20 20" fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                      d="M6.625 3.125C6.34886 3.125 6.125 3.34886 6.125 3.625V4.875H13.375C14.3415 4.875 15.125 5.6585 15.125 6.625V13.875H16.375C16.6511 13.875 16.875 13.6511 16.875 13.375V3.625C16.875 3.34886 16.6511 3.125 16.375 3.125H6.625ZM15.125 15.125H16.375C17.3415 15.125 18.125 14.3415 18.125 13.375V3.625C18.125 2.6585 17.3415 1.875 16.375 1.875H6.625C5.6585 1.875 4.875 2.6585 4.875 3.625V4.875H3.625C2.6585 4.875 1.875 5.6585 1.875 6.625V16.375C1.875 17.3415 2.6585 18.125 3.625 18.125H13.375C14.3415 18.125 15.125 17.3415 15.125 16.375V15.125ZM13.875 6.625C13.875 6.34886 13.6511 6.125 13.375 6.125H3.625C3.34886 6.125 3.125 6.34886 3.125 6.625V16.375C3.125 16.6511 3.34886 16.875 3.625 16.875H13.375C13.6511 16.875 13.875 16.6511 13.875 16.375V6.625Z"
                                                                      fill="rgba(51, 102, 255, 1)"></path>
                                                            </svg>
                                                        </a>
                                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="bank-info-row">
                            <div class="bank-info-cell">Số tài khoản</div>
                            <div class="bank-info-cell">
                                <div class="bank-info-value">
                                    {{$account_number}}
                                    <span id="sepay_copy_account_number">
                                                        <a id="sepay_copy_account_number_btn" href="javascript:;">
                                                            <svg width="15" height="15" viewBox="0 0 20 20" fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                      d="M6.625 3.125C6.34886 3.125 6.125 3.34886 6.125 3.625V4.875H13.375C14.3415 4.875 15.125 5.6585 15.125 6.625V13.875H16.375C16.6511 13.875 16.875 13.6511 16.875 13.375V3.625C16.875 3.34886 16.6511 3.125 16.375 3.125H6.625ZM15.125 15.125H16.375C17.3415 15.125 18.125 14.3415 18.125 13.375V3.625C18.125 2.6585 17.3415 1.875 16.375 1.875H6.625C5.6585 1.875 4.875 2.6585 4.875 3.625V4.875H3.625C2.6585 4.875 1.875 5.6585 1.875 6.625V16.375C1.875 17.3415 2.6585 18.125 3.625 18.125H13.375C14.3415 18.125 15.125 17.3415 15.125 16.375V15.125ZM13.875 6.625C13.875 6.34886 13.6511 6.125 13.375 6.125H3.625C3.34886 6.125 3.125 6.34886 3.125 6.625V16.375C3.125 16.6511 3.34886 16.875 3.625 16.875H13.375C13.6511 16.875 13.875 16.6511 13.875 16.375V6.625Z"
                                                                      fill="rgba(51, 102, 255, 1)"></path>
                                                            </svg>
                                                        </a>
                                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="bank-info-row">
                            <div class="bank-info-cell">Số tiền</div>
                            <div class="bank-info-cell">
                                <div class="bank-info-value">
                                                    <span class="font-bold" id="copy_amount" data-value="{{$price}}">
                                                        <span class="woocommerce-Price-amount amount">{{number_format($price)}}<span
                                                                class="woocommerce-Price-currencySymbol">₫</span></span>                                                    </span>
                                    <span id="sepay_copy_amount">
                                                        <a id="sepay_copy_amount_btn" href="javascript:;">
                                                            <svg width="15" height="15" viewBox="0 0 20 20" fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                      d="M6.625 3.125C6.34886 3.125 6.125 3.34886 6.125 3.625V4.875H13.375C14.3415 4.875 15.125 5.6585 15.125 6.625V13.875H16.375C16.6511 13.875 16.875 13.6511 16.875 13.375V3.625C16.875 3.34886 16.6511 3.125 16.375 3.125H6.625ZM15.125 15.125H16.375C17.3415 15.125 18.125 14.3415 18.125 13.375V3.625C18.125 2.6585 17.3415 1.875 16.375 1.875H6.625C5.6585 1.875 4.875 2.6585 4.875 3.625V4.875H3.625C2.6585 4.875 1.875 5.6585 1.875 6.625V16.375C1.875 17.3415 2.6585 18.125 3.625 18.125H13.375C14.3415 18.125 15.125 17.3415 15.125 16.375V15.125ZM13.875 6.625C13.875 6.34886 13.6511 6.125 13.375 6.125H3.625C3.34886 6.125 3.125 6.34886 3.125 6.625V16.375C3.125 16.6511 3.34886 16.875 3.625 16.875H13.375C13.6511 16.875 13.875 16.6511 13.875 16.375V6.625Z"
                                                                      fill="rgba(51, 102, 255, 1)"></path>
                                                            </svg>
                                                        </a>
                                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="bank-info-row">
                            <div class="bank-info-cell">Nội dung CK</div>
                            <div class="bank-info-cell">
                                <div class="bank-info-value">
                                    <span id="copy_memo" class="font-bold">{{$description}}</span>
                                    <span id="sepay_copy_transfer_content">
                                                        <a id="sepay_copy_transfer_content_btn" href="javascript:;">
                                                            <svg width="15" height="15" viewBox="0 0 20 20" fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                      d="M6.625 3.125C6.34886 3.125 6.125 3.34886 6.125 3.625V4.875H13.375C14.3415 4.875 15.125 5.6585 15.125 6.625V13.875H16.375C16.6511 13.875 16.875 13.6511 16.875 13.375V3.625C16.875 3.34886 16.6511 3.125 16.375 3.125H6.625ZM15.125 15.125H16.375C17.3415 15.125 18.125 14.3415 18.125 13.375V3.625C18.125 2.6585 17.3415 1.875 16.375 1.875H6.625C5.6585 1.875 4.875 2.6585 4.875 3.625V4.875H3.625C2.6585 4.875 1.875 5.6585 1.875 6.625V16.375C1.875 17.3415 2.6585 18.125 3.625 18.125H13.375C14.3415 18.125 15.125 17.3415 15.125 16.375V15.125ZM13.875 6.625C13.875 6.34886 13.6511 6.125 13.375 6.125H3.625C3.34886 6.125 3.125 6.34886 3.125 6.625V16.375C3.125 16.6511 3.34886 16.875 3.625 16.875H13.375C13.6511 16.875 13.875 16.6511 13.875 16.375V6.625Z"
                                                                      fill="rgba(51, 102, 255, 1)"></path>
                                                            </svg>
                                                        </a>
                                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="note">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path fill-rule="evenodd"
                              d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z"
                              clip-rule="evenodd"></path>
                    </svg>
                    <span>Lưu ý: Vui lòng giữ nguyên nội dung chuyển khoản <b>{{$description}}</b> để xác nhận thanh toán tự
                                        động.</span>
                </div>
            </div>

            <div></div>
        </div>
        <!-- /Manual method -->
    </div>
    <div class="sepay-pay-footer">
        Trạng thái: Chờ thanh toán <img
            src="https://nhannguyensharing.com/wp-content/plugins/sepay-gateway/imgs/loading.gif">
    </div>
</div>




<script>
    $(document).ready(function () {
        const myInterval = setInterval(function () {
            $.ajax({
                type: "get",
                url: '{{route('payment.check.ajax')}}?invoice={{$description}}',
                success: function (response) {
                    if(response.status){
                        clearInterval(myInterval);
                        alert(response.msg)
                        window.location.href = '{{route('my.courses')}}'
                    }
                }
            });
        }, 3000)
    });
</script>
<script>

    document.addEventListener('DOMContentLoaded', function () {
        setTimeout(function () {
            const vietQR = new VietQR();
            vietQR
                .setBeneficiaryOrganization("{{$bank_name}}", "{{$account_number??''}}")
                .setTransactionAmount("{{$price}}")
                .setAdditionalDataFieldTemplate("{{$description}}");

            const qrString = vietQR.build();

            // Tạo QR code
            new QRCode(document.getElementById("qrcode"), {
                text: qrString,
                width: 200,
                height: 200,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H
            });
        }, 500);
    });
</script>

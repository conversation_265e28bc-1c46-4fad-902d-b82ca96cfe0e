<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('withdraws', function (Blueprint $table) {
            $table->id();
            $table->integer("user_id")->comment("Người rút tiền");
            $table->integer("amount")->default(0)->comment("Số tiền rút");
            $table->enum("status", [0, 1, 2])->default(0)->comment("Trạng thái 0=>Pending, 1=>Completed, 2=>Canceled");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('withdraws');
    }
};

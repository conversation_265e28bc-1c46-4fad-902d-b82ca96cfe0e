<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        
        // Update existing affiliate addon with permissions
        $affiliateAddon = DB::table('addons')->where('name_addons', 'my.affiliate')->first();
        if ($affiliateAddon) {
            DB::table('addons')->where('id', $affiliateAddon->id)->update([
                'permission_keys' => json_encode(['admin.affiliate', 'admin.affiliate.withdraws', 'admin.affiliate.settings'])
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('addons', function (Blueprint $table) {
            $table->dropColumn('permission_keys');
        });
    }
};

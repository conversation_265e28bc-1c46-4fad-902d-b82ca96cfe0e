<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('lead_opportunities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // ID của học viên
            $table->unsignedBigInteger('sale_id'); // ID của nhân viên sale phụ trách
            $table->string('name'); // Tên cơ hội
            $table->decimal('potential_value', 10, 2)->default(0); // Giá trị cơ hội
            $table->enum('status', ['open', 'won', 'lost', 'postponed'])->default('open');
            $table->text('notes')->nullable();
            $table->decimal('probability', 5, 2)->default(0); // Xác suất thành công (%)
            $table->datetime('expected_close_date')->nullable(); // Ngày dự kiến kết thúc
            $table->string('lost_reason')->nullable(); // Lý do mất cơ hội nếu trạng thái lost
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('sale_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('lead_opportunities');
    }
}; 
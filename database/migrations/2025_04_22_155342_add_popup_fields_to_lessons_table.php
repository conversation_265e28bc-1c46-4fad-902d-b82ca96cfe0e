<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            $table->unsignedBigInteger('popup_id')->nullable()->after('description');
            $table->integer('popup_time')->default(30)->nullable()->after('popup_id');
            $table->foreign('popup_id')->references('id')->on('popups')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            $table->dropForeign(['popup_id']);
            $table->dropColumn('popup_id');
            $table->dropColumn('popup_time');
        });
    }
};

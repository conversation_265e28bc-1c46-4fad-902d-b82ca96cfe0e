<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;
use Carbon\Carbon;

class AddCustomScriptSettings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $settings = [
            [
                'type' => 'custom_script_head',
                'description' => '',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'type' => 'custom_script_body_start',
                'description' => '',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'type' => 'custom_script_footer',
                'description' => '',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]
        ];

        foreach ($settings as $setting) {
            if (!Setting::where('type', $setting['type'])->exists()) {
                Setting::create($setting);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Setting::whereIn('type', [
            'custom_script_head',
            'custom_script_body_start',
            'custom_script_footer'
        ])->delete();
    }
}

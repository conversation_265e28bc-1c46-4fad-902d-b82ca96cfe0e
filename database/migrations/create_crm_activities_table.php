<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('crm_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // ID của học viên
            $table->unsignedBigInteger('sale_id'); // ID của nhân viên sale
            $table->enum('activity_type', ['call', 'email', 'message', 'meeting', 'note']);
            $table->text('description')->nullable();
            $table->enum('result', ['success', 'pending', 'failed', 'no_answer'])->default('pending');
            $table->datetime('next_follow_up')->nullable();
            $table->enum('lead_status', ['new', 'contacted', 'nurturing', 'qualified', 'proposal', 'closed_won', 'closed_lost'])->default('new');
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('sale_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('crm_activities');
    }
}; 
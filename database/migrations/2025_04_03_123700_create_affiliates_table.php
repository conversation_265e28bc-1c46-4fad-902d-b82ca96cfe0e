<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliates', function (Blueprint $table) {
            $table->id();
            $table->integer("user_id")->comment("Người làm affiliate");
            $table->integer("customer_id")->comment("Khách hàng đã mua khóa học");
            $table->integer("course_id")->comment("Khóa học nào");
            $table->integer("amount")->default(0)->comment("Số tiền hưởng hoa hồng");
            $table->tinyInteger("status")->default(2)->comment("Trạng thái 0=>Canceled, 1=>Completed(Đã + vào balance), 2=>Waiting(Đã + vào earning chưa + vào balance)");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliates');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('withdraws', function (Blueprint $table) {
            $table->string('ip_address')->nullable()->after('status');
            $table->decimal('previous_balance', 15, 2)->nullable()->after('amount');
            $table->timestamp('request_date')->nullable()->after('ip_address');
            $table->timestamp('processed_date')->nullable()->after('request_date');
            $table->string('processed_by')->nullable()->after('processed_date');
            $table->unsignedBigInteger('processor_id')->nullable()->after('processed_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('withdraws', function (Blueprint $table) {
            $table->dropColumn('ip_address');
            $table->dropColumn('previous_balance');
            $table->dropColumn('request_date');
            $table->dropColumn('processed_date');
            $table->dropColumn('processed_by');
            $table->dropColumn('processor_id');
        });
    }
};

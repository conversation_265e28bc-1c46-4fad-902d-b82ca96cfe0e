<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('builder_pages', function (Blueprint $table) {
            $table->boolean('disable_bootstrap')->default(0)->after('is_page_courses');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('builder_pages', function (Blueprint $table) {
            $table->dropColumn('disable_bootstrap');
        });
    }
};

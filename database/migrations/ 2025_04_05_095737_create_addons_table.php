<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('addons', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('author');
            $table->string('name_addons')->unique();
            $table->text('description')->nullable();
            $table->string('thumbnail')->nullable();
            $table->boolean('status')->default(0);
            $table->json('permission_keys')->nullable();
            $table->timestamps();
        });

        // Insert default Affiliate addon
        DB::table('addons')->insert([
            'id' => 1,
            'name' => 'Affiliate',
            'author' => 'TOPID',
            'name_addons' => 'my.affiliate',
            'description' => 'T<PERSON>h năng Affiliate cho phép người dùng chia sẻ khóa học với người khác và nhận hoa hồng khi có người đăng ký qua liên kết của bạn. Đây là cách đơn giản để vừa học, vừa kiếm thêm thu nhập, đồng thời giúp lan tỏa giá trị của khóa học đến nhiều người hơn.',
            'permission_keys' => json_encode(['admin.affiliate', 'admin.affiliate.withdraws', 'admin.affiliate.settings']),
            'status' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('addons');
    }
}; 
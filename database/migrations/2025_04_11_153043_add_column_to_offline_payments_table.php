<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('offline_payments', function (Blueprint $table) {
            $table->integer('is_add_earning_affiliate')->default(0)->comment('0: chưa cộng, 1: đã cộng vào ví earning hay chưa');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('offline_payments', function (Blueprint $table) {

            $table->dropColumn('is_add_earning_affiliate');
        });
    }
};

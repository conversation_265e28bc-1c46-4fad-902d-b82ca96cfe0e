<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('offline_payments', function (Blueprint $table) {
            $table->integer('course_id')->nullable();
            $table->integer('affiliate_id')->nullable()->comment("user_id của người làm affiliate");
            $table->boolean('is_approve_affiliate')->default(false)->comment("Đã duyệt + tiền sau 7 ngày cho user làm aff hay chưa? .0: chưa duyệt, 1: đã duyệt");
            $table->integer('affiliate_amount')->nullable()->comment("số tiền affiliate");
            $table->string("transaction_content")->nullable()->comment("Nội dung của giao dịch");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('offline_payments', function (Blueprint $table) {
            $table->dropColumn('course_id');
            $table->dropColumn('affiliate_id');
            $table->dropColumn('is_approve_affiliate');
            $table->dropColumn('affiliate_amount');
            $table->dropColumn('transaction_content');
        });
    }
};

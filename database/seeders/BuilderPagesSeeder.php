<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Builder_page;
use Illuminate\Support\Facades\DB;

class BuilderPagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define builder pages data
        $pages = [
            [
                'name' => 'Khóa học MST',
                'identifier' => 'course-mst',
                'is_permanent' => 1,
                'is_page_courses' => 1,
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'name' => 'Trang chủ khóa học helen spa',
                'identifier' => 'helen-spa',
                'is_permanent' => 1,
                'is_page_courses' => 1,
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Khóa học helen spa',
                'identifier' => 'detail-helen-spa',
                'is_permanent' => 2,
                'is_page_courses' => 1,
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Đậu Xanh',
                'identifier' => 'detail-dau-xanh',
                'is_permanent' => 1,
                'is_page_courses' => 1,
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Trang chủ Đậu Xanh',
                'identifier' => 'home-dau-xanh',
                'is_permanent' => 1,
                'is_page_courses' => 1,
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Trang chủ Spa Diệu Thu',
                'identifier' => 'home-spa-dieu-thu',
                'is_permanent' => 1,
                'is_page_courses' => 1,
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Trang chủ Mai Kim Quý',
                'identifier' => 'home-mai-kim-quy',
                'is_permanent' => 1,
                'is_page_courses' => 1,
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // Upsert the page based on identifier
        foreach ($pages as $page) {
            DB::table('builder_pages')->updateOrInsert(
                ['identifier' => $page['identifier']], // Condition to check if exists
                $page // Data to update or create
            );
        }
    }
}

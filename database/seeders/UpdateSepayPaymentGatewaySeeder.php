<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateSepayPaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $keysData = [
            "api_key" => "sk_test_a1b2c3d4e5f6g7h8i9j0",
            "name" => "<PERSON>",
            "bank" => "OCB",
            "account_number" => "**********"
        ];

        // Update bảng payment_gateways
        DB::table('payment_gateways')
            ->where('identifier', 'sepay')
            ->update([
                'keys' => json_encode($keysData),
                'updated_at' => now()
            ]);
    }
}

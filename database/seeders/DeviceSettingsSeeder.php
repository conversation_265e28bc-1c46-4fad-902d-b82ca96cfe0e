<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class DeviceSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $settings = [
            [
                'type' => 'device_limitation',
                'description' => '3',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'type' => 'allow_simultaneous_login',
                'description' => '1',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'type' => 'simultaneous_login_limit',
                'description' => '5',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];

        // First check if the settings already exist
        foreach ($settings as $setting) {
            $exists = DB::table('settings')
                ->where('type', $setting['type'])
                ->exists();
            
            if (!$exists) {
                DB::table('settings')->insert($setting);
            }
        }
    }
} 